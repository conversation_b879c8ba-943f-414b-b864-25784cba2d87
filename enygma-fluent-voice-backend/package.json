{"name": "enygma-fluent-voice-backend", "version": "2.0.0", "description": "Modern Voice AI Backend with modular architecture", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development node --watch index.js", "start": "cross-env NODE_ENV=production node index.js", "start:legacy": "cross-env NODE_ENV=production node legacy/index.js", "test": "jest", "test:health": "curl -f http://localhost:3001/health || echo 'Health check failed'", "test:services": "node scripts/test-services.js", "lint": "eslint . --ext .js --ignore-pattern node_modules", "lint:fix": "eslint . --ext .js --ignore-pattern node_modules --fix", "migrate": "node scripts/migrate.js", "rollback": "node rollback.js", "clean": "rm -rf backup_* legacy/ && echo 'Cleaned up migration files'", "build": "echo 'No build step required for Node.js backend'", "deploy-gcloud": "gcloud run deploy --source .", "deploy-azure": "az containerapp up --source .", "docker:build": "docker build -t voice-ai-backend .", "docker:run": "docker run -p 3001:8080 --env-file .env voice-ai-backend", "logs": "tail -f logs/app-$(date +%Y-%m-%d).log", "logs:error": "tail -f logs/error-$(date +%Y-%m-%d).log", "monitor": "node scripts/monitor.js", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|backup_*' > docs/file-system.md"}, "engines": {"node": ">=18.x"}, "dependencies": {"@google-cloud/speech": "^6.7.0", "@google-cloud/vertexai": "^1.9.2", "axios": "^1.8.4", "better-logging": "^5.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "csurf": "^1.11.0", "dotenv": "^16.4.7", "express": "^4.21.2", "hpp": "^0.2.3", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "openai": "^5.12.2", "socket.io": "^4.8.1"}}