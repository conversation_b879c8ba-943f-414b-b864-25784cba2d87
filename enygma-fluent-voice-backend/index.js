/**
 * Modern Voice AI Backend
 * Clean architecture with proper separation of concerns
 */
const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')

// Core imports
const { environment, securityManager } = require('./core/config')
const { logger, performance } = require('./core/utils')

// API imports
const { HealthController } = require('./api/controllers/HealthController')
const { createApiRoutes } = require('./api/routes')
const { createApiMiddleware } = require('./api/middleware')

// Socket imports
const { VoiceSessionHandler } = require('./socket/handlers/VoiceSessionHandler')
const { createSocketMiddleware } = require('./socket/middleware')

// Better logging
require('better-logging')(console)

class VoiceAIServer {
  constructor() {
    this.app = express()
    this.httpServer = createServer(this.app)

    // Configure Socket.IO with CORS
    this.io = new Server(this.httpServer, {
      cors: {
        origin: environment.security.corsOrigins,
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.activeSessions = new Map()

    this.healthController = new HealthController()
  }

  async initialize() {
    const timer = performance.startTimer('server_initialization')

    try {
      logger.info('🚀 Initializing Voice AI Backend...')

      // Configure Express security and middleware
      this.configureExpress()

      // Setup API routes
      this.setupApiRoutes()

      // Configure Socket.IO
      this.configureSocketIO()

      // Setup error handling
      this.setupErrorHandling()

      logger.info('✅ Voice AI Backend initialized successfully')

    } catch (error) {
      logger.error('❌ Server initialization failed:', error)
      throw error
    } finally {
      performance.endTimer('server_initialization')
    }
  }

  configureExpress() {
    logger.debug('⚙️ Configuring Express middleware...')

    // Security configuration
    securityManager.configureExpress(this.app)

    // Additional middleware
    createApiMiddleware(this.app)

    // Health check route (no auth required)
    this.app.get('/health', (req, res) => this.healthController.getHealth(req, res))

    logger.debug('✅ Express middleware configured')
  }

  setupApiRoutes() {
    logger.debug('🛣️ Setting up API routes...')

    // Create API routes with controllers
    const apiRoutes = createApiRoutes({
      healthController: this.healthController
    })

    // Mount API routes with authentication
    this.app.use('/api', securityManager.validateApiKey.bind(securityManager), apiRoutes)

    logger.debug('✅ API routes configured')
  }

  configureSocketIO() {
    logger.debug('🔌 Configuring Socket.IO...')

    // Configure Socket.IO security
    securityManager.configureSocketSecurity(this.io)

    // Setup socket middleware
    createSocketMiddleware(this.io)

    // Handle connections
    this.io.on('connection', (socket) => {
      this.handleSocketConnection(socket)
    })

    logger.debug('✅ Socket.IO configured')
  }

  handleSocketConnection(socket) {
    const timer = performance.startTimer('socket_connection')

    try {
      const provider = socket.handshake.query.provider || environment.providers.default

      logger.info(`🔌 New socket connection: ${socket.id} (provider: ${provider})`)

      // Create voice session handler
      const sessionHandler = new VoiceSessionHandler(socket, { provider })

      // Store session reference
      this.activeSessions.set(socket.id, sessionHandler)

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info(`🔌 Socket disconnected: ${socket.id} (reason: ${reason})`)

        // Cleanup session
        const session = this.activeSessions.get(socket.id)
        if (session) {
          session.cleanup()
          this.activeSessions.delete(socket.id)
        }

        performance.endTimer('socket_connection')
      })

    } catch (error) {
      logger.error(`Failed to handle socket connection ${socket.id}:`, error)
      socket.emit('error', {
        message: 'Failed to establish voice session',
        code: 'SESSION_INITIALIZATION_FAILED'
      })
      socket.disconnect(true)
    }
  }

  setupErrorHandling() {
    // Global error handler for unhandled routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method
      })
    })

    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Express error handler:', error)

      res.status(error.status || 500).json({
        error: environment.isProduction ? 'Internal server error' : error.message,
        code: error.code || 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      })
    })

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error)
      this.gracefulShutdown('UNCAUGHT_EXCEPTION')
    })

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason)
      this.gracefulShutdown('UNHANDLED_REJECTION')
    })

    // Handle termination signals
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'))
  }

  async start() {
    const timer = performance.startTimer('server_startup')

    try {
      await this.initialize()

      const port = environment.server.port

      this.httpServer.listen(port, () => {
        logger.info(`🚀 Voice AI Backend running on port ${port}`)
        logger.info(`📊 Environment: ${environment.nodeEnv}`)
        logger.info(`🔧 Provider: ${environment.providers.default}`)
        logger.info(`🔍 Log level: ${environment.server.logLevel}`)

        // Log configuration summary
        const configSummary = environment.getSummary()
        logger.debug('Configuration summary:', configSummary)
      })

    } catch (error) {
      logger.error('❌ Failed to start server:', error)
      process.exit(1)
    } finally {
      performance.endTimer('server_startup')
    }
  }

  async gracefulShutdown(signal) {
    logger.info(`🛑 Received ${signal}, starting graceful shutdown...`)

    const shutdownTimer = performance.startTimer('graceful_shutdown')

    try {
      // Stop accepting new connections
      this.httpServer.close(() => {
        logger.info('📡 HTTP server closed')
      })

      // Close all active socket connections
      logger.info(`🔌 Closing ${this.activeSessions.size} active sessions...`)
      for (const [socketId, session] of this.activeSessions) {
        try {
          session.cleanup()
          session.socket.disconnect(true)
        } catch (error) {
          logger.warn(`Error closing session ${socketId}:`, error.message)
        }
      }
      this.activeSessions.clear()

      // Close Socket.IO server
      this.io.close(() => {
        logger.info('🔌 Socket.IO server closed')
      })

      // Cleanup performance monitoring
      performance.cleanup()

      logger.info('✅ Graceful shutdown completed')

    } catch (error) {
      logger.error('❌ Error during shutdown:', error)
    } finally {
      performance.endTimer('graceful_shutdown')
      process.exit(0)
    }
  }

  // Public methods for external access
  getActiveSessionsCount() {
    return this.activeSessions.size
  }

  getSessionStatus(socketId) {
    const session = this.activeSessions.get(socketId)
    return session ? session.getSessionStatus() : null
  }

  getAllSessionsStatus() {
    const sessions = {}
    for (const [socketId, session] of this.activeSessions) {
      sessions[socketId] = session.getSessionStatus()
    }
    return sessions
  }
}

// Create and start server
const server = new VoiceAIServer()

// Start server
server.start().catch((error) => {
  logger.error('💥 Fatal error starting server:', error)
  process.exit(1)
})

// Export for testing
module.exports = { VoiceAIServer, server }