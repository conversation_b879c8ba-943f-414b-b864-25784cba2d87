/**
 * Socket.IO middleware configuration
 * Authentication, rate limiting, and monitoring
 */
const { logger, performance } = require('../../core/utils')
const { environment } = require('../../core/config')
const { ERROR_CODES } = require('../../core/types/voice')

/**
 * Create socket middleware stack
 */
function createSocketMiddleware(io) {
  logger.debug('🔌 Setting up Socket.IO middleware...')

  // Authentication middleware
  io.use(authenticationMiddleware)

  // Rate limiting middleware
  io.use(rateLimitingMiddleware)

  // Validation middleware
  io.use(validationMiddleware)

  // Logging middleware
  io.use(loggingMiddleware)

  // Error handling
  setupErrorHandling(io)

  logger.debug('✅ Socket.IO middleware configured')
}

/**
 * Authentication middleware
 */
function authenticationMiddleware(socket, next) {
  try {
    const apiKey = socket.handshake.auth.apiKey ||
                   socket.handshake.headers['x-api-key'] ||
                   socket.handshake.query.apiKey

    if (!apiKey) {
      logger.warn(`🚫 Socket connection rejected - No API key: ${socket.handshake.address}`)
      return next(new Error('Authentication required'))
    }

    if (apiKey !== environment.security.apiKey) {
      logger.warn(`🚫 Socket connection rejected - Invalid API key: ${socket.handshake.address}`)
      return next(new Error('Invalid API key'))
    }

    // Add user context to socket
    socket.user = {
      authenticated: true,
      connectionTime: new Date(),
      ip: socket.handshake.address,
      userAgent: socket.handshake.headers['user-agent'] || 'Unknown'
    }

    logger.debug(`✅ Socket authenticated: ${socket.id}`)
    next()

  } catch (error) {
    logger.error('Authentication middleware error:', error)
    next(new Error('Authentication failed'))
  }
}

/**
 * Rate limiting middleware
 */
function rateLimitingMiddleware(socket, next) {
  if (!environment.security.enableRateLimit) {
    return next()
  }

  try {
    const ip = socket.handshake.address
    const now = Date.now()
    const windowMs = 60000 // 1 minute
    const maxConnections = 5 // connections per minute per IP

    // Simple in-memory rate limiter (use Redis in production)
    if (!global.socketRateLimit) {
      global.socketRateLimit = new Map()
    }

    const clientRecord = global.socketRateLimit.get(ip) || []
    const recentConnections = clientRecord.filter(time => now - time < windowMs)

    if (recentConnections.length >= maxConnections) {
      logger.warn(`🚫 Socket rate limit exceeded for IP: ${ip}`)
      return next(new Error('Rate limit exceeded'))
    }

    // Add current connection
    recentConnections.push(now)
    global.socketRateLimit.set(ip, recentConnections)

    // Cleanup old entries periodically
    if (Math.random() < 0.1) { // 10% chance
      cleanupRateLimit(global.socketRateLimit, windowMs)
    }

    next()

  } catch (error) {
    logger.error('Rate limiting middleware error:', error)
    next(error)
  }
}

/**
 * Validation middleware
 */
function validationMiddleware(socket, next) {
  try {
    // Validate provider parameter
    const provider = socket.handshake.query.provider
    const validProviders = ['custom', 'google', 'azure']
    if (provider && !validProviders.includes(provider)) {
      logger.warn(`🚫 Invalid provider requested: ${provider}`)
      return next(new Error('Invalid provider'))
    }

    // Validate user agent (more permissive in development)
    const userAgent = socket.handshake.headers['user-agent']
    if (environment.isProduction && (!userAgent || userAgent.length > 500)) {
      logger.warn(`🚫 Invalid user agent: ${socket.handshake.address}`)
      return next(new Error('Invalid user agent'))
    }

    // In development, just log if user agent is missing
    if (!environment.isProduction && !userAgent) {
      logger.debug(`⚠️ No user agent provided from: ${socket.handshake.address}`)
    }

    // Validate origin in production
    if (environment.isProduction) {
      const origin = socket.handshake.headers.origin
      const allowedOrigins = environment.security.corsOrigins

      if (origin && !allowedOrigins.includes(origin)) {
        logger.warn(`🚫 Invalid origin: ${origin}`)
        return next(new Error('Origin not allowed'))
      }
    }

    next()

  } catch (error) {
    logger.error('Validation middleware error:', error)
    next(error)
  }
}

/**
 * Logging middleware
 */
function loggingMiddleware(socket, next) {
  try {
    const connectionInfo = {
      socketId: socket.id,
      ip: socket.handshake.address,
      userAgent: socket.handshake.headers['user-agent'],
      provider: socket.handshake.query.provider || 'default',
      timestamp: new Date().toISOString()
    }

    logger.info(`🔌 Socket connection attempt:`, connectionInfo)

    // Start connection timer
    performance.startTimer(`socket_connection_${socket.id}`)

    next()

  } catch (error) {
    logger.error('Logging middleware error:', error)
    next(error)
  }
}

/**
 * Setup error handling for Socket.IO
 */
function setupErrorHandling(io) {
  // Connection error handler
  io.engine.on('connection_error', (err) => {
    logger.error('🚫 Socket.IO connection error:', {
      message: err.message,
      code: err.code,
      description: err.description,
      context: err.context,
      type: err.type
    })
  })

  // Global error handler for socket events
  io.on('connect_error', (error) => {
    logger.error('🚫 Socket connect error:', error)
  })
}

/**
 * Message-level middleware for individual events
 */
function createMessageMiddleware() {
  return {
    // Audio data validation middleware
    validateAudioData: (handler) => {
      return async (audioData) => {
        const timer = performance.startTimer('audio_validation')

        try {
          const { validation } = require('../../core/utils')
          const result = validation.validateAudioBuffer(audioData)

          if (!result.valid) {
            this.emit('error', {
              code: ERROR_CODES.INVALID_AUDIO_BUFFER,
              message: result.errors.join(', '),
              timestamp: new Date().toISOString()
            })
            return
          }

          await handler.call(this, audioData)

        } catch (error) {
          logger.error(`Audio validation error for socket ${this.id}:`, error)
          this.emit('error', {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: 'Audio validation failed',
            timestamp: new Date().toISOString()
          })
        } finally {
          performance.endTimer('audio_validation')
        }
      }
    },

    // Rate limiting for message events
    rateLimit: (maxMessages = 1000, windowMs = 60000) => {
      const messageCounts = new Map()

      return (handler) => {
        return async (...args) => {
          const now = Date.now()
          const socketId = this.id

          // Get or create message record
          let record = messageCounts.get(socketId) || { count: 0, windowStart: now }

          // Reset if window expired
          if (now - record.windowStart > windowMs) {
            record = { count: 0, windowStart: now }
          }

          // Check rate limit
          if (record.count >= maxMessages) {
            logger.warn(`🚫 Message rate limit exceeded for socket: ${socketId}`)
            this.emit('error', {
              code: ERROR_CODES.SOCKET_RATE_LIMIT,
              message: 'Message rate limit exceeded',
              timestamp: new Date().toISOString()
            })
            return
          }

          // Increment counter and call handler
          record.count++
          messageCounts.set(socketId, record)

          await handler.call(this, ...args)
        }
      }
    },

    // Performance monitoring middleware
    monitor: (eventName) => {
      return (handler) => {
        return async (...args) => {
          const timer = performance.startTimer(`socket_${eventName}_${this.id}`)

          try {
            await handler.call(this, ...args)
          } finally {
            performance.endTimer(`socket_${eventName}_${this.id}`)
          }
        }
      }
    }
  }
}

/**
 * Cleanup old rate limit entries
 */
function cleanupRateLimit(rateLimitMap, windowMs) {
  const now = Date.now()

  for (const [ip, connections] of rateLimitMap.entries()) {
    const validConnections = connections.filter(time => now - time < windowMs)

    if (validConnections.length === 0) {
      rateLimitMap.delete(ip)
    } else {
      rateLimitMap.set(ip, validConnections)
    }
  }
}

/**
 * Socket session monitoring utilities
 */
function createSessionMonitor() {
  const activeSessions = new Map()

  return {
    addSession: (socket) => {
      activeSessions.set(socket.id, {
        socketId: socket.id,
        startTime: Date.now(),
        ip: socket.handshake.address,
        provider: socket.handshake.query.provider,
        messageCount: 0,
        lastActivity: Date.now()
      })
    },

    updateActivity: (socketId) => {
      const session = activeSessions.get(socketId)
      if (session) {
        session.lastActivity = Date.now()
        session.messageCount++
      }
    },

    removeSession: (socketId) => {
      const session = activeSessions.get(socketId)
      if (session) {
        const duration = Date.now() - session.startTime
        logger.info(`📊 Session ended: ${socketId}, duration: ${duration}ms, messages: ${session.messageCount}`)
        activeSessions.delete(socketId)
      }
    },

    getActiveSessions: () => Array.from(activeSessions.values()),

    getSessionCount: () => activeSessions.size,

    cleanup: () => {
      const now = Date.now()
      const timeout = 30 * 60 * 1000 // 30 minutes

      for (const [socketId, session] of activeSessions.entries()) {
        if (now - session.lastActivity > timeout) {
          logger.warn(`🧹 Cleaning up stale session: ${socketId}`)
          activeSessions.delete(socketId)
        }
      }
    }
  }
}

module.exports = {
  createSocketMiddleware,
  createMessageMiddleware,
  createSessionMonitor,
  authenticationMiddleware,
  rateLimitingMiddleware,
  validationMiddleware,
  loggingMiddleware
}