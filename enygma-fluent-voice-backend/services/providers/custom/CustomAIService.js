const axios = require('axios')
const { IAIService } = require('../../interfaces/IAIService')
const { customLog } = require('../../../core/utils/logger')

class CustomAIService extends IAIService {
  constructor(config) {
    super()
    this.config = config || {}
    this.axiosInstance = null

    this.init()
  }

  init() {
    try {
      // Validar configuración
      if (!this.config.apiUrl) {
        throw new Error('Custom LLM API URL not configured')
      }

      if (!this.config.apiKey) {
        throw new Error('Custom LLM API key not configured')
      }

      // Crear axios instance para el LLM
      this.axiosInstance = axios.create({
        baseURL: this.config.apiUrl,
        headers: {
          Authorization: `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout || 30000
      })

      customLog('Custom AI Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Custom AI Service:', error.message)
      throw error
    }
  }

  async generateReply(conversationHistory, options = {}) {
    // Usar validación de la interfaz
    const validatedHistory = this.validateConversationHistory(conversationHistory)

    if (!this.axiosInstance) {
      throw new Error('Custom AI service not initialized')
    }

    try {
      // Determinar el preset según el modo de juego
      const presetId = options.mode === 'ia_vs_player'
        ? this.config.presetIaVsPlayer
        : this.config.presetGenCharBot

      // Formatear la conversación para el LLM personalizado
      const messages = validatedHistory.map(turn => ({
        role: turn.user ? 'user' : 'assistant',
        content: turn.content.trim()
      }))

      // Preparar el payload según tu formato
      const payload = {
        query: messages[messages.length - 1]?.content || '',
        preset_id: presetId,
        messages: messages.slice(0, -1), // Historial sin el mensaje actual
        options: {
          mode: options.mode || 'player_vs_ia',
          character: options.character,
          sessionId: options.sessionId
        }
      }

      customLog('Sending request to Custom LLM with preset:', presetId)

      const response = await this.axiosInstance.post('/generate', payload)

      // Validar respuesta
      if (!response.data) {
        throw new Error('Empty response from Custom LLM service')
      }

      // Manejar diferentes formatos de respuesta
      let responseText = ''

      if (response.data.output) {
        responseText = response.data.output
      } else if (response.data.response) {
        responseText = response.data.response
      } else if (typeof response.data === 'string') {
        responseText = response.data
      } else {
        throw new Error('Invalid response format from Custom LLM service')
      }

      // Log de uso si está disponible
      if (response.data.sizes) {
        customLog('Custom LLM usage:', {
          prompt_tokens: response.data.sizes.prompt_tokens,
          completion_tokens: response.data.sizes.completion_tokens,
          total_tokens: response.data.sizes.total_tokens,
          provider: 'custom'
        })
      }

      return responseText.trim()

    } catch (error) {
      customLog('Error generating Custom LLM reply:', error.message || error)
      throw this.handleProviderError(error)
    }
  }

  async generateCharacter(sessionId) {
    try {
      const payload = {
        query: 'Genera un nuevo personaje para el juego',
        preset_id: this.config.presetGenCharBot,
        options: {
          mode: 'character_generation',
          sessionId: sessionId
        }
      }

      const response = await this.axiosInstance.post('/generate', payload)

      if (!response.data || !response.data.output) {
        throw new Error('No character generated')
      }

      return response.data.output.trim()

    } catch (error) {
      customLog('Error generating character:', error.message)
      throw error
    }
  }

  async resetSession(sessionId) {
    try {
      if (!sessionId) return

      const payload = {
        session_id: sessionId,
        action: 'reset'
      }

      await this.axiosInstance.post('/reset', payload)
      customLog('Session reset successfully:', sessionId)

    } catch (error) {
      customLog('Error resetting session:', error.message)
      // No lanzar error, el reset es opcional
    }
  }

  async testConnection() {
    try {
      if (!this.axiosInstance) {
        throw new Error('Service not initialized')
      }

      // Test básico con el preset por defecto
      const testPayload = {
        query: 'test',
        preset_id: this.config.presetIaVsPlayer || this.config.presetGenCharBot
      }

      const response = await this.axiosInstance.post('/generate', testPayload)

      if (response.data) {
        customLog('Custom LLM connection test successful')
        return true
      }

      return false

    } catch (error) {
      customLog('Custom LLM connection test failed:', error.message)
      return false
    }
  }

  getStatus() {
    return {
      provider: 'custom',
      service: 'custom-llm',
      isInitialized: this.axiosInstance !== null,
      apiUrl: this.config.apiUrl,
      presetIaVsPlayer: this.config.presetIaVsPlayer,
      presetGenCharBot: this.config.presetGenCharBot,
      timeout: this.config.timeout || 30000
    }
  }

  getModelInfo() {
    return {
      provider: 'custom',
      service: 'custom-llm',
      apiUrl: this.config.apiUrl,
      presets: {
        iaVsPlayer: this.config.presetIaVsPlayer,
        genCharBot: this.config.presetGenCharBot
      },
      features: [
        'chat_completions',
        'preset_support',
        'session_management',
        'character_generation'
      ]
    }
  }

  // Override interface error handling for Custom LLM specific errors
  handleProviderError(error) {
    const errorMessage = error.message || error.toString()

    // Custom LLM specific error handling
    if (error.response) {
      const status = error.response.status
      if (status === 401) {
        return new Error('Custom LLM authentication failed. Please check API key.')
      } else if (status === 429) {
        return new Error('Custom LLM rate limit exceeded. Please try again later.')
      } else if (status === 400) {
        return new Error('Invalid request to Custom LLM service.')
      } else if (status >= 500) {
        return new Error('Custom LLM service error. Please try again later.')
      }
    }

    if (errorMessage.includes('timeout')) {
      return new Error('Custom LLM service request timed out. Please try again.')
    }

    // Fall back to base interface error handling
    return super.handleProviderError(error)
  }

  // Cleanup method
  cleanup() {
    try {
      if (this.axiosInstance) {
        this.axiosInstance = null
      }
      customLog('Custom AI Service cleaned up')
    } catch (error) {
      customLog('Error during Custom AI cleanup:', error.message)
    }
  }
}

module.exports = { CustomAIService }
