const { SpeechClient } = require('@google-cloud/speech')
const { ISpeechService } = require('../../interfaces/ISpeechService')
const { customLog } = require('../../../core/utils/logger')

class GoogleSpeechService extends ISpeechService {
  constructor(config) {
    super()
    this.config = config || {}
    this.client = null
    this.recognizeStream = null
    this.isRecognizing = false

    this.init()
  }

  init() {
    try {
      // Initialize Google Speech client
      this.client = new SpeechClient()

      // Validate configuration
      if (!this.config.config) {
        throw new Error('Google Speech configuration not provided')
      }

      customLog('Google Speech Service initialized successfully')

    } catch (error) {
      customLog('Error initializing Google Speech Service:', error.message)
      throw error
    }
  }

  createRecognizer(onResult, onError, onSessionStopped) {
    try {
      // Clean up existing stream
      this.cleanup()

      customLog('Creating new Google Speech recognition stream')

      // Validate speech client
      if (!this.client) {
        throw new Error('Speech client not initialized')
      }

      // Create the streaming recognition request
      const request = {
        config: this.config.config,
        interimResults: this.config.interimResults || true
      }

      const stream = this.client
        .streamingRecognize(request)
        .on('error', (error) => {
          customLog('Error in Google Speech recognition:', error.message || error)
          this.isRecognizing = false

          // Clean up stream on error
          this.cleanup()

          // Notify client of transcription error
          if (onError) {
            onError(new Error('Speech recognition temporarily unavailable. Please try again.'))
          }
        })
        .on('data', (data) => {
          try {
            if (data.results[0] && data.results[0].alternatives[0]) {
              const transcript = data.results[0].alternatives[0].transcript.trim()
              const confidence = data.results[0].alternatives[0].confidence || 0
              const isFinal = data.results[0].isFinal || false

              if (transcript && onResult) {
                onResult({
                  text: transcript,
                  isFinal: isFinal,
                  confidence: confidence,
                  provider: 'google'
                })
              }
            }
          } catch (dataError) {
            customLog('Error processing Google Speech data:', dataError.message)
            if (onError) {
              onError(new Error('Error processing audio data. Please try again.'))
            }
          }
        })
        .on('end', () => {
          customLog('Google Speech stream ended')
          this.isRecognizing = false
          if (onSessionStopped) {
            onSessionStopped()
          }
        })

      this.recognizeStream = stream
      this.isRecognizing = true

      return stream

    } catch (error) {
      customLog('Error creating Google Speech recognizer:', error.message)
      throw error
    }
  }

  startContinuousRecognition() {
    if (!this.recognizeStream) {
      throw new Error('Speech recognizer not initialized')
    }

    try {
      customLog('Google Speech continuous recognition started')
      this.isRecognizing = true
      // Google's streaming recognition starts automatically when stream is created
    } catch (error) {
      customLog('Error in startContinuousRecognition:', error.message)
      throw error
    }
  }

  stopRecognition() {
    if (this.recognizeStream && this.isRecognizing) {
      try {
        customLog('Stopping Google Speech recognition')
        this.recognizeStream.removeAllListeners()
        this.recognizeStream.end()
        this.isRecognizing = false
      } catch (error) {
        customLog('Error in stopRecognition:', error.message)
      }
    }
  }

  writeAudioData(audioBuffer) {
    // Use interface validation
    this.validateAudioData(audioBuffer)

    if (!this.recognizeStream) {
      throw new Error('Recognition stream not initialized')
    }

    try {
      const buffer = Buffer.from(new Uint8Array(audioBuffer))

      // Validate buffer size after conversion
      if (buffer.length === 0) {
        customLog('Warning: Received empty audio buffer')
        return
      }

      this.recognizeStream.write(buffer)

    } catch (error) {
      customLog('Error writing audio data to Google Speech stream:', error.message)
      throw error
    }
  }

  closeStream() {
    if (this.recognizeStream) {
      try {
        this.recognizeStream.end()
        customLog('Google Speech stream closed')
      } catch (error) {
        customLog('Error closing Google Speech stream:', error.message)
      }
    }
  }

  cleanup() {
    try {
      this.stopRecognition()

      if (this.recognizeStream) {
        try {
          if (!this.recognizeStream.destroyed) {
            this.recognizeStream.removeAllListeners()
            this.recognizeStream.end()
          }
        } catch (streamError) {
          customLog('Error cleaning up recognition stream:', streamError.message)
        } finally {
          this.recognizeStream = null
        }
      }

      this.isRecognizing = false
      customLog('Google Speech Service cleaned up')

    } catch (error) {
      customLog('Error during Google Speech cleanup:', error.message)
    }
  }

  isReady() {
    return this.client !== null && !this.isRecognizing
  }

  getRecognitionState() {
    return {
      isRecognizing: this.isRecognizing,
      hasRecognizer: this.recognizeStream !== null,
      hasStream: this.recognizeStream !== null,
      provider: 'google'
    }
  }

  getConfig() {
    return {
      provider: 'google',
      encoding: this.config.config?.encoding || 'LINEAR16',
      sampleRateHertz: this.config.config?.sampleRateHertz || 16000,
      languageCode: this.config.config?.languageCode || 'es-ES',
      model: this.config.config?.model || 'latest_long',
      useEnhanced: this.config.config?.useEnhanced || true,
      interimResults: this.config.interimResults || true
    }
  }

  // Google-specific method for testing connection
  async testConnection() {
    try {
      if (!this.client) {
        throw new Error('Speech client not initialized')
      }

      // Create a simple recognition request to test the connection
      const request = {
        config: {
          encoding: 'LINEAR16',
          sampleRateHertz: 16000,
          languageCode: 'es-ES'
        },
        audio: {
          content: Buffer.alloc(1600) // 100ms of silence at 16kHz
        }
      }

      const [response] = await this.client.recognize(request)
      customLog('Google Speech connection test successful')
      return true

    } catch (error) {
      customLog('Google Speech connection test failed:', error.message)
      return false
    }
  }

  // Google-specific method to get supported languages
  async getSupportedLanguages() {
    try {
      // Google Speech supports many languages, returning common ones
      return [
        { code: 'es-ES', name: 'Spanish (Spain)', provider: 'google' },
        { code: 'es-MX', name: 'Spanish (Mexico)', provider: 'google' },
        { code: 'es-AR', name: 'Spanish (Argentina)', provider: 'google' },
        { code: 'en-US', name: 'English (US)', provider: 'google' },
        { code: 'en-GB', name: 'English (UK)', provider: 'google' },
        { code: 'fr-FR', name: 'French', provider: 'google' },
        { code: 'de-DE', name: 'German', provider: 'google' },
        { code: 'it-IT', name: 'Italian', provider: 'google' },
        { code: 'pt-BR', name: 'Portuguese (Brazil)', provider: 'google' }
      ]
    } catch (error) {
      customLog('Error getting supported languages:', error.message)
      return []
    }
  }

  // Google-specific method to update configuration
  updateConfiguration(newConfig) {
    const updatedConfig = { ...this.config, ...newConfig }

    // Validate critical configuration changes
    if (newConfig.config) {
      const requiredFields = ['encoding', 'sampleRateHertz', 'languageCode']
      for (const field of requiredFields) {
        if (!newConfig.config[field]) {
          throw new Error(`Missing required configuration field: ${field}`)
        }
      }
    }

    this.config = updatedConfig
    customLog('Google Speech configuration updated')

    return this.getConfig()
  }

  // Method to get available models
  getAvailableModels() {
    return [
      { name: 'latest_long', description: 'Latest model for long audio', provider: 'google' },
      { name: 'latest_short', description: 'Latest model for short audio', provider: 'google' },
      { name: 'command_and_search', description: 'Optimized for commands and search', provider: 'google' },
      { name: 'phone_call', description: 'Optimized for phone call audio', provider: 'google' },
      { name: 'video', description: 'Optimized for video audio', provider: 'google' },
      { name: 'default', description: 'Default model', provider: 'google' }
    ]
  }
}

module.exports = { GoogleSpeechService }
