// Azure Providers
const { AzureSpeechService } = require('../providers/azure/AzureSpeechService')
const { AzureAIService } = require('../providers/azure/AzureAIService')
const { AzureTTSService } = require('../providers/azure/AzureTTSService')

// Google Providers
const { GoogleSpeechService } = require('../providers/google/GoogleSpeechService')
const { GoogleAIService } = require('../providers/google/GoogleAIService')
const { GoogleTTSService } = require('../providers/google/GoogleTTSService')

// Custom Providers (TUS SERVIDORES)
const { CustomSpeechService } = require('../providers/custom/CustomSpeechService')
const { CustomAIService } = require('../providers/custom/CustomAIService')
const { CustomTTSService } = require('../providers/custom/CustomTTSService')

const { customLog } = require('../../core/utils/logger')

/**
 * Factory class for creating AI services based on provider
 * Supports Azure, Google, and Custom providers with automatic configuration
 */
class ServiceFactory {
  constructor() {
    this.providers = new Set(['azure', 'google', 'custom'])
    this.serviceCache = new Map()
    this.configurations = new Map()
  }

  /**
   * Creates a complete set of services for the specified provider
   * @param {string} provider - Provider name ('azure', 'google', or 'custom')
   * @param {Object} config - Configuration object for the provider
   * @param {Object} options - Additional options
   * @returns {Object} Object containing speech, ai, and tts services
   */
  static create(provider = 'custom', config = {}, options = {}) {
    const factory = new ServiceFactory()
    return factory.createServices(provider, config, options)
  }

  /**
   * Creates services for the specified provider
   * @param {string} provider - Provider name
   * @param {Object} config - Configuration object
   * @param {Object} options - Additional options
   * @returns {Object} Services object
   */
  createServices(provider, config = {}, options = {}) {
    this.validateProvider(provider)

    const cacheKey = `${provider}_${JSON.stringify(config)}`

    // Return cached services if available and not forced to recreate
    if (this.serviceCache.has(cacheKey) && !options.forceRecreate) {
      customLog(`Returning cached services for provider: ${provider}`)
      return this.serviceCache.get(cacheKey)
    }

    customLog(`Creating new services for provider: ${provider}`)

    let services

    try {
      switch (provider.toLowerCase()) {
        case 'azure':
          services = this.createAzureServices(config)
          break
        case 'google':
          services = this.createGoogleServices(config)
          break
        case 'custom':
          services = this.createCustomServices(config)
          break
        default:
          throw new Error(`Unsupported provider: ${provider}`)
      }

      // Add common methods to services object
      this.enhanceServicesObject(services, provider)

      // Cache the services
      this.serviceCache.set(cacheKey, services)
      this.configurations.set(cacheKey, { provider, config, options })

      customLog(`Services created successfully for provider: ${provider}`)
      return services

    } catch (error) {
      customLog(`Error creating services for provider ${provider}:`, error.message)
      throw new Error(`Failed to create services for ${provider}: ${error.message}`)
    }
  }

  /**
   * Creates Azure services
   * @param {Object} config - Azure configuration
   * @returns {Object} Azure services
   */
  createAzureServices(config) {
    const azureConfig = this.normalizeAzureConfig(config)

    return {
      speech: new AzureSpeechService(azureConfig.speech),
      ai: new AzureAIService(azureConfig.ai),
      tts: new AzureTTSService(azureConfig.tts),
      provider: 'azure'
    }
  }

  /**
   * Creates Google services
   * @param {Object} config - Google configuration
   * @returns {Object} Google services
   */
  createGoogleServices(config) {
    const googleConfig = this.normalizeGoogleConfig(config)

    return {
      speech: new GoogleSpeechService(googleConfig.speech),
      ai: new GoogleAIService(googleConfig.ai),
      tts: new GoogleTTSService(googleConfig.tts),
      provider: 'google'
    }
  }

  /**
   * Creates Custom services (TUS SERVIDORES)
   * @param {Object} config - Custom configuration
   * @returns {Object} Custom services
   */
  createCustomServices(config) {
    const customConfig = this.normalizeCustomConfig(config)

    return {
      speech: new CustomSpeechService(customConfig.speech),
      ai: new CustomAIService(customConfig.ai),
      tts: new CustomTTSService(customConfig.tts),
      provider: 'custom'
    }
  }

  /**
   * Normalizes Azure configuration
   * @param {Object} config - Raw configuration
   * @returns {Object} Normalized configuration
   */
  normalizeAzureConfig(config) {
    const defaultConfig = {
      speech: {
        subscriptionKey: process.env.AZURE_SPEECH_KEY,
        region: process.env.AZURE_SPEECH_REGION || 'westeurope',
        language: 'es-ES',
        format: 'Detailed',
        profanity: 'Masked',
        enableDictation: true
      },
      ai: {
        endpoint: process.env.AZURE_OPENAI_ENDPOINT,
        apiKey: process.env.AZURE_OPENAI_KEY,
        deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-4',
        apiVersion: '2024-08-01-preview',
        systemPrompt: config.systemPrompt || process.env.AI_PROMPT,
        maxTokens: 500,
        temperature: 0.7,
        topP: 0.9
      },
      tts: {
        subscriptionKey: process.env.AZURE_SPEECH_KEY,
        region: process.env.AZURE_SPEECH_REGION || 'westeurope',
        voice: 'es-ES-ElviraNeural',
        outputFormat: 'Audio16Khz32KBitRateMonoMp3',
        speakingRate: '0%',
        pitch: '0%'
      }
    }

    return this.mergeDeep(defaultConfig, config)
  }

  /**
   * Normalizes Google configuration
   * @param {Object} config - Raw configuration
   * @returns {Object} Normalized configuration
   */
  normalizeGoogleConfig(config) {
    const defaultConfig = {
      speech: {
        config: {
          encoding: 'LINEAR16',
          sampleRateHertz: 16000,
          languageCode: 'es-ES',
          model: 'latest_long',
          useEnhanced: true
        },
        interimResults: true
      },
      ai: {
        project: process.env.GOOGLE_CLOUD_PROJECT || 'sandbox-innovacion-3',
        location: process.env.GOOGLE_CLOUD_LOCATION || 'europe-southwest1',
        model: 'gemini-2.0-flash-001',
        systemPrompt: config.systemPrompt || process.env.AI_PROMPT,
        maxTokens: 500,
        temperature: 0.7,
        topP: 0.9
      },
      tts: {
        baseURL: process.env.AUDIO_BACKEND_BASE_URL,
        apiKey: process.env.AUDIO_BACKEND_API_KEY,
        voiceId: 'Ximena',
        outputFormat: 'mp3',
        timeout: 30000
      }
    }

    return this.mergeDeep(defaultConfig, config)
  }

  /**
   * Normalizes Custom configuration (TUS SERVIDORES)
   * @param {Object} config - Raw configuration
   * @returns {Object} Normalized configuration
   */
  normalizeCustomConfig(config) {
    const defaultConfig = {
      speech: {
        // Para el stream de voz - configuración básica
        encoding: 'LINEAR16',
        sampleRateHertz: 16000,
        languageCode: 'es-ES',
        bufferProcessing: true
      },
      ai: {
        apiUrl: process.env.IA_API_URL || 'https://dev.dl2discovery.org/llm-api/v1/',
        apiKey: process.env.IA_API_KEY || '9dcd0147-11e2-4e9e-aaf3-05e1498ce828',
        presetIaVsPlayer: process.env.IA_PRESETID_IA_VS_PLAYER || 'mapp-Claude_enygma_V2',
        presetGenCharBot: process.env.IA_PRESETID_GENCHARBOT || 'mapp-gen-char-bot',
        timeout: 30000,
        maxTokens: 500,
        temperature: 0.7
      },
      tts: {
        apiUrl: process.env.SPEECH_API_URL || 'https://dev.dl2discovery.org/sts/api/v1/',
        apiKey: process.env.SPEECH_API_KEY || 'b075e3a3-3cd8-4b65-ba5e-735e32ec3251',
        defaultVoice: 'es-ES-ElviraNeural',
        language: 'es-ES',
        gender: 'female',
        rate: '0%',
        outputFormat: 'mp3',
        timeout: 30000,
        maxContentLength: 10 * 1024 * 1024 // 10MB
      }
    }

    return this.mergeDeep(defaultConfig, config)
  }

  /**
   * Enhances services object with common utility methods
   * @param {Object} services - Services object
   * @param {string} provider - Provider name
   */
  enhanceServicesObject(services, provider) {
    // Add test all connections method
    services.testAllConnections = async () => {
      const results = {}

      try {
        results.speech = await services.speech.testConnection()
      } catch (error) {
        results.speech = false
        customLog('Speech service test failed:', error.message)
      }

      try {
        results.ai = await services.ai.testConnection()
      } catch (error) {
        results.ai = false
        customLog('AI service test failed:', error.message)
      }

      try {
        results.tts = await services.tts.testConnection()
      } catch (error) {
        results.tts = false
        customLog('TTS service test failed:', error.message)
      }

      return {
        provider,
        allHealthy: Object.values(results).every(Boolean),
        services: results
      }
    }

    // Add get all statuses method
    services.getAllStatuses = () => {
      return {
        provider,
        speech: services.speech.getStatus(),
        ai: services.ai.getStatus(),
        tts: services.tts.getStatus()
      }
    }

    // Add cleanup all method
    services.cleanupAll = () => {
      try {
        if (services.speech.cleanup) services.speech.cleanup()
        if (services.ai.cleanup) services.ai.cleanup()
        if (services.tts.cleanup) services.tts.cleanup()
        customLog(`All ${provider} services cleaned up`)
      } catch (error) {
        customLog(`Error cleaning up ${provider} services:`, error.message)
      }
    }

    // Add provider info
    services.getProviderInfo = () => {
      return {
        provider,
        speech: services.speech.getConfig ? services.speech.getConfig() : null,
        ai: services.ai.getModelInfo ? services.ai.getModelInfo() : null,
        tts: services.tts.getStatus()
      }
    }

    // Métodos específicos para Custom provider
    if (provider === 'custom') {
      services.generateCharacter = async (sessionId) => {
        return await services.ai.generateCharacter(sessionId)
      }

      services.resetSession = async (sessionId) => {
        return await services.ai.resetSession(sessionId)
      }

      services.setVoice = async (voiceName) => {
        return await services.tts.setVoice(voiceName)
      }

      services.getAvailableVoices = async () => {
        return await services.tts.getAvailableVoices()
      }

      services.getStreamStats = () => {
        return services.speech.getStreamStats ? services.speech.getStreamStats() : null
      }

      services.processAccumulatedAudio = async () => {
        return services.speech.processAccumulatedAudio ?
          await services.speech.processAccumulatedAudio() : null
      }
    }
  }

  /**
   * Validates provider name
   * @param {string} provider - Provider to validate
   * @throws {Error} If provider is not supported
   */
  validateProvider(provider) {
    if (!provider || typeof provider !== 'string') {
      throw new Error('Provider must be a non-empty string')
    }

    if (!this.providers.has(provider.toLowerCase())) {
      throw new Error(`Unsupported provider: ${provider}. Supported providers: ${Array.from(this.providers).join(', ')}`)
    }
  }

  /**
   * Deep merge utility for configuration objects
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} Merged object
   */
  mergeDeep(target, source) {
    const result = { ...target }

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.mergeDeep(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
    }

    return result
  }

  /**
   * Get supported providers
   * @returns {Array} List of supported providers
   */
  static getSupportedProviders() {
    return ['azure', 'google', 'custom']
  }

  /**
   * Clear all cached services
   */
  clearCache() {
    this.serviceCache.clear()
    this.configurations.clear()
    customLog('Service cache cleared')
  }

  /**
   * Get cached configurations
   * @returns {Map} Map of configurations
   */
  getCachedConfigurations() {
    return new Map(this.configurations)
  }

  /**
   * Create services from environment variables
   * @param {string} provider - Provider name
   * @returns {Object} Services object
   */
  static createFromEnv(provider = null) {
    // Auto-detect provider if not specified
    if (!provider) {
      if (process.env.IA_API_URL && process.env.SPEECH_API_URL) {
        provider = 'custom'
      } else if (process.env.AZURE_OPENAI_ENDPOINT && process.env.AZURE_SPEECH_KEY) {
        provider = 'azure'
      } else if (process.env.GOOGLE_CLOUD_PROJECT && process.env.AUDIO_BACKEND_BASE_URL) {
        provider = 'google'
      } else {
        throw new Error('Cannot auto-detect provider. Please specify provider or set environment variables.')
      }
    }

    customLog(`Creating services from environment for provider: ${provider}`)
    return ServiceFactory.create(provider)
  }

  /**
   * Create services with custom configuration validation
   * @param {string} provider - Provider name
   * @param {Object} config - Configuration object
   * @param {Function} validator - Custom validation function
   * @returns {Object} Services object
   */
  static createWithValidation(provider, config, validator) {
    if (validator && typeof validator === 'function') {
      const validationResult = validator(provider, config)
      if (!validationResult.valid) {
        throw new Error(`Configuration validation failed: ${validationResult.message}`)
      }
      config = validationResult.config || config
    }

    return ServiceFactory.create(provider, config)
  }
}

module.exports = { ServiceFactory }
