/**
 * Interface para servicios de Text-to-Speech
 * Define los métodos que deben implementar todos los proveedores de TTS
 */
class ITTSService {
  /**
   * Inicializa el servicio de text-to-speech
   * @throws {Error} Si la inicialización falla
   */
  init() {
    throw new Error('Method init() must be implemented')
  }

  /**
   * Genera audio a partir de texto
   * @param {string} text - Texto a convertir en audio
   * @returns {Promise<Buffer>} Buffer con datos de audio
   * @throws {Error} Si la generación falla
   */
  async generateSpeech(text) {
    throw new Error('Method generateSpeech() must be implemented')
  }

  /**
   * Prueba la conexión con el servicio de TTS
   * @returns {Promise<boolean>} True si la conexión es exitosa
   */
  async testConnection() {
    throw new Error('Method testConnection() must be implemented')
  }

  /**
   * Obtiene las voces disponibles
   * @returns {Promise<Array>} Lista de voces disponibles
   */
  async getAvailableVoices() {
    throw new Error('Method getAvailableVoices() must be implemented')
  }

  /**
   * Obtiene el estado del servicio
   * @returns {Object} Estado actual del servicio
   */
  getStatus() {
    throw new Error('Method getStatus() must be implemented')
  }

  /**
   * Limpia el texto para mejorar la síntesis de voz
   * @param {string} text - Texto a limpiar
   * @returns {string} Texto limpio
   */
  cleanTextForSpeech(text) {
    if (typeof text !== 'string') {
      throw new Error('Text must be a string')
    }

    return text
      // Remove markdown links
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // Remove markdown bold/italic
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      .replace(/__([^_]+)__/g, '$1')
      .replace(/_([^_]+)_/g, '$1')
      // Remove code blocks
      .replace(/`([^`]+)`/g, '$1')
      .replace(/```[\s\S]*?```/g, '')
      // Clean list markers
      .replace(/^[\s-*]+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove quotes
      .replace(/^\s*>\s+/gm, '')
      // Remove headers
      .replace(/^#{1,6}\s+/gm, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Handle brand names for better pronunciation
      .replace(/Movistar\+/g, 'Movistar Plus')
      .trim()
  }

  /**
   * Valida el texto de entrada para TTS
   * @param {string} text - Texto a validar
   * @param {number} maxLength - Longitud máxima permitida
   * @returns {string} Texto validado
   * @throws {Error} Si el texto no es válido
   */
  validateTextInput(text, maxLength = 5000) {
    if (typeof text !== 'string') {
      throw new Error('Text input must be a string')
    }

    if (text.trim() === '') {
      throw new Error('Text input cannot be empty')
    }

    if (text.length > maxLength) {
      throw new Error(`Text too long for speech generation: ${text.length} characters (max: ${maxLength})`)
    }

    return text.trim()
  }

  /**
   * Valida el buffer de audio generado
   * @param {Buffer} audioBuffer - Buffer de audio a validar
   * @returns {Buffer} Buffer validado
   * @throws {Error} Si el buffer no es válido
   */
  validateAudioOutput(audioBuffer) {
    if (!audioBuffer) {
      throw new Error('Audio buffer is required')
    }

    if (!Buffer.isBuffer(audioBuffer)) {
      throw new Error('Audio output must be a Buffer')
    }

    if (audioBuffer.length === 0) {
      throw new Error('TTS service returned empty audio data')
    }

    // Check reasonable file size limits
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (audioBuffer.length > maxSize) {
      throw new Error(`Generated audio too large: ${audioBuffer.length} bytes (max: ${maxSize})`)
    }

    return audioBuffer
  }

  /**
   * Maneja errores específicos del proveedor TTS
   * @param {Error} error - Error original
   * @returns {Error} Error normalizado
   */
  handleTTSError(error) {
    const errorMessage = error.message || error.toString()

    if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
      return new Error('TTS service quota exceeded. Please try again later.')
    } else if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return new Error('TTS service authentication failed. Please check API key.')
    } else if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return new Error('Network error connecting to TTS service. Please check your connection.')
    } else if (errorMessage.includes('timeout')) {
      return new Error('TTS service request timed out. Please try again.')
    } else if (errorMessage.includes('voice') || errorMessage.includes('language')) {
      return new Error('TTS service voice or language configuration error.')
    } else {
      return new Error(`TTS service error: ${errorMessage}`)
    }
  }

  /**
   * Crea SSML (Speech Synthesis Markup Language) básico
   * @param {string} text - Texto base
   * @param {Object} options - Opciones para SSML (rate, pitch, volume)
   * @returns {string} SSML formateado
   */
  createBasicSSML(text, options = {}) {
    const { rate = '0%', pitch = '0%', volume = '0%' } = options

    // Escape XML special characters
    const escapedText = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')

    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis">
        <prosody rate="${rate}" pitch="${pitch}" volume="${volume}">
          ${escapedText}
        </prosody>
      </speak>
    `.trim()
  }
}

module.exports = { ITTSService }
