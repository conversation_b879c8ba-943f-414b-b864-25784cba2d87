/**
 * Interface para servicios de Speech-to-Text
 * Define los métodos que deben implementar todos los proveedores de STT
 */
class ISpeechService {
  /**
   * Inicializa el servicio de speech-to-text
   * @throws {Error} Si la inicialización falla
   */
  init() {
    throw new Error('Method init() must be implemented')
  }

  /**
   * Crea un recognizer/stream para reconocimiento continuo
   * @param {Function} onResult - Callback para resultados de reconocimiento
   * @param {Function} onError - Callback para errores
   * @param {Function} onSessionStopped - Callback cuando la sesión termina
   * @returns {Object} Recognizer instance
   */
  createRecognizer(onResult, onError, onSessionStopped) {
    throw new Error('Method createRecognizer() must be implemented')
  }

  /**
   * Inicia el reconocimiento continuo de voz
   * @throws {Error} Si el reconocimiento no puede iniciarse
   */
  startContinuousRecognition() {
    throw new Error('Method startContinuousRecognition() must be implemented')
  }

  /**
   * Detiene el reconocimiento de voz
   */
  stopRecognition() {
    throw new Error('Method stopRecognition() must be implemented')
  }

  /**
   * Escribe datos de audio al stream de reconocimiento
   * @param {Buffer|Uint8Array} audioBuffer - Datos de audio
   * @throws {Error} Si no se puede escribir al stream
   */
  writeAudioData(audioBuffer) {
    throw new Error('Method writeAudioData() must be implemented')
  }

  /**
   * Cierra el stream de audio
   */
  closeStream() {
    throw new Error('Method closeStream() must be implemented')
  }

  /**
   * Limpia recursos del servicio
   */
  cleanup() {
    throw new Error('Method cleanup() must be implemented')
  }

  /**
   * Verifica si el servicio está listo para usar
   * @returns {boolean} True si está listo
   */
  isReady() {
    throw new Error('Method isReady() must be implemented')
  }

  /**
   * Obtiene el estado actual del reconocimiento
   * @returns {Object} Estado del reconocimiento
   */
  getRecognitionState() {
    throw new Error('Method getRecognitionState() must be implemented')
  }

  /**
   * Obtiene la configuración del servicio
   * @returns {Object} Configuración actual
   */
  getConfig() {
    throw new Error('Method getConfig() must be implemented')
  }

  /**
   * Valida los datos de audio antes de procesarlos
   * @param {Buffer|Uint8Array} audioBuffer - Datos de audio a validar
   * @returns {boolean} True si los datos son válidos
   * @throws {Error} Si los datos no son válidos
   */
  validateAudioData(audioBuffer) {
    if (!audioBuffer) {
      throw new Error('Audio buffer is required')
    }

    if (typeof audioBuffer !== 'object') {
      throw new Error('Invalid audio buffer format')
    }

    const maxSize = 1024 * 1024 // 1MB limit
    if (audioBuffer.byteLength && audioBuffer.byteLength > maxSize) {
      throw new Error(`Audio buffer too large: ${audioBuffer.byteLength} bytes (max: ${maxSize})`)
    }

    if (audioBuffer.length === 0 || (audioBuffer.byteLength && audioBuffer.byteLength === 0)) {
      throw new Error('Audio buffer cannot be empty')
    }

    return true
  }
}

module.exports = { ISpeechService }
