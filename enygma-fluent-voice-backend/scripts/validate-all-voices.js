// scripts/validate-all-voices.js
require('dotenv').config()
const axios = require('axios')
const fs = require('fs')
const path = require('path')

async function validateAllVoices() {
  console.log('🎤 Validando todas las voces TTS disponibles...\n')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  if (!config.apiUrl || !config.apiKey) {
    console.log('❌ Faltan configuraciones de entorno (SPEECH_API_URL o SPEECH_API_KEY)')
    return
  }

  // Voces encontradas por el script anterior
  const discoveredVoices = [
    '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Ximena DragonHD'
  ]

  const testResults = {
    working: [],
    failed: [],
    details: {}
  }

  // Crear directorio para samples de audio (opcional)
  const samplesDir = path.join(__dirname, '../audio-samples')
  if (!fs.existsSync(samplesDir)) {
    fs.mkdirSync(samplesDir, { recursive: true })
  }

  console.log('🔄 Probando cada voz individualmente...\n')

  for (const voice of discoveredVoices) {
    console.log(`🎵 Probando voz: ${voice}`)

    try {
      // Usar el formato correcto que encontramos
      const payload = {
        input_text: `Hola, soy ${voice}. Esta es una prueba de voz.`,
        voice_params: {
          voice_name: voice
        }
      }

      console.log(`   📤 Enviando: ${JSON.stringify(payload)}`)

      const startTime = Date.now()
      const response = await axios.post(`${config.apiUrl}/t2s`, payload, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        responseType: 'arraybuffer',
        timeout: 15000
      })
      const endTime = Date.now()

      const audioSize = response.data.byteLength
      const responseTime = endTime - startTime

      console.log(`   ✅ SUCCESS!`)
      console.log(`   📊 Tamaño audio: ${audioSize} bytes`)
      console.log(`   ⏱️  Tiempo respuesta: ${responseTime}ms`)
      console.log(`   📋 Content-Type: ${response.headers['content-type'] || 'No especificado'}`)

      // Guardar sample de audio (opcional)
      const audioPath = path.join(samplesDir, `${voice.replace(/\s+/g, '_')}_sample.mp3`)
      fs.writeFileSync(audioPath, response.data)
      console.log(`   💾 Sample guardado: ${audioPath}`)

      testResults.working.push(voice)
      testResults.details[voice] = {
        status: 'success',
        audioSize,
        responseTime,
        contentType: response.headers['content-type'],
        samplePath: audioPath
      }

    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`)

      if (error.response) {
        console.log(`   📊 Status: ${error.response.status}`)
        console.log(`   📋 Status Text: ${error.response.statusText}`)

        // Intentar leer el error si es texto
        if (error.response.data && error.response.data.byteLength < 1000) {
          try {
            const errorText = Buffer.from(error.response.data).toString()
            console.log(`   🔍 Error details: ${errorText.substring(0, 300)}`)
          } catch (e) {
            console.log(`   🔍 Error details: [Binary data no legible]`)
          }
        }
      }

      testResults.failed.push(voice)
      testResults.details[voice] = {
        status: 'failed',
        error: error.message,
        statusCode: error.response?.status,
        statusText: error.response?.statusText
      }
    }

    console.log() // Línea en blanco
  }

  // Resumen final
  console.log('📊 RESUMEN DE RESULTADOS')
  console.log('========================\n')

  console.log(`✅ Voces funcionando (${testResults.working.length}):`)
  testResults.working.forEach(voice => {
    const details = testResults.details[voice]
    console.log(`   • ${voice} - ${details.audioSize} bytes en ${details.responseTime}ms`)
  })

  if (testResults.failed.length > 0) {
    console.log(`\n❌ Voces con problemas (${testResults.failed.length}):`)
    testResults.failed.forEach(voice => {
      const details = testResults.details[voice]
      console.log(`   • ${voice} - Error: ${details.error}`)
    })
  }

  // Generar configuración para el servicio
  console.log('\n🔧 CONFIGURACIÓN RECOMENDADA PARA EL SERVICIO:')
  console.log('===============================================\n')

  const configOutput = {
    availableVoices: testResults.working,
    defaultVoice: testResults.working.includes('Elvira') ? 'Elvira' : testResults.working[0],
    voicesByGender: {
      female: testResults.working.filter(v => ['Dalia', 'Elvira', 'Irene', 'Triana', 'Ximena DragonHD'].includes(v)),
      male: testResults.working.filter(v => ['Elias', 'Jorge', 'Saul'].includes(v))
    },
    payloadFormat: {
      input_text: "string (requerido)",
      voice_params: {
        voice_name: "string (uno de availableVoices)"
      }
    }
  }

  console.log('```javascript')
  console.log('// Añadir a tu CustomTTSService o configuración')
  console.log(JSON.stringify(configOutput, null, 2))
  console.log('```')

  // Guardar resultados en archivo
  const resultsPath = path.join(__dirname, '../voice-validation-results.json')
  fs.writeFileSync(resultsPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    testResults,
    config: configOutput
  }, null, 2))

  console.log(`\n💾 Resultados completos guardados en: ${resultsPath}`)

  if (testResults.working.length > 0) {
    console.log(`\n🎉 ¡${testResults.working.length} voces validadas exitosamente!`)
    console.log('   Puedes usar cualquiera de estas voces en tu servicio TTS.')
  } else {
    console.log('\n⚠️  Ninguna voz funcionó correctamente. Revisa la configuración del servicio.')
  }

  return testResults
}

// Test adicional: verificar el endpoint available_voices
async function testAvailableVoicesEndpoint() {
  console.log('\n🔍 BONUS: Verificando endpoint available_voices...')

  const config = {
    apiUrl: process.env.SPEECH_API_URL,
    apiKey: process.env.SPEECH_API_KEY
  }

  try {
    const response = await axios.post(`${config.apiUrl}/available_voices`, {}, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`
      }
    })

    console.log('✅ available_voices responde correctamente')
    console.log('📋 Formato respuesta:', typeof response.data)
    console.log('📊 Contenido:', response.data)

    if (typeof response.data === 'string' && response.data.includes(',')) {
      const voices = response.data.split(',')
      console.log('🎤 Voces parseadas:', voices)
    }

  } catch (error) {
    console.log('❌ Error en available_voices:', error.message)
  }
}

// Ejecutar validación completa
async function main() {
  const results = await validateAllVoices()
  await testAvailableVoicesEndpoint()

  console.log('\n✨ Validación completa terminada!')
  console.log('   Revisa los archivos generados y usa las voces validadas en tu servicio.')
}

main().catch(console.error)
