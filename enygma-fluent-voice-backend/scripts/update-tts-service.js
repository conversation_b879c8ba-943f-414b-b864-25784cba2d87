// scripts/update-tts-service.js
require('dotenv').config()
const fs = require('fs')
const path = require('path')

async function updateTTSService() {
  console.log('🔧 Actualizando servicios TTS con voces validadas...\n')

  // Leer resultados de validación
  const resultsPath = path.join(__dirname, '../voice-validation-results.json')

  if (!fs.existsSync(resultsPath)) {
    console.log('❌ No se encontraron resultados de validación.')
    console.log('   Ejecuta primero: node scripts/validate-all-voices.js')
    return
  }

  const validationData = JSON.parse(fs.readFileSync(resultsPath, 'utf8'))
  const workingVoices = validationData.testResults.working

  if (workingVoices.length === 0) {
    console.log('❌ No hay voces validadas para usar.')
    return
  }

  console.log(`✅ Encontradas ${workingVoices.length} voces validadas:`)
  workingVoices.forEach(voice => console.log(`   • ${voice}`))
  console.log()

  // 1. Actualizar CustomTTSService
  await updateCustomTTSService(workingVoices, validationData)

  // 2. Actualizar configuración de entorno
  await updateEnvironmentConfig(workingVoices, validationData)

  // 3. Crear archivo de configuración de voces
  await createVoicesConfig(workingVoices, validationData)

  console.log('🎉 ¡Actualización completa!')
  console.log('   Reinicia tu servidor para aplicar los cambios.')
}

async function updateCustomTTSService(workingVoices, validationData) {
  console.log('📝 Actualizando CustomTTSService...')

  const servicePath = path.join(__dirname, '../core/services/tts/CustomTTSService.js')

  if (!fs.existsSync(servicePath)) {
    console.log('⚠️  CustomTTSService.js no encontrado. Creando nuevo archivo...')
    await createCustomTTSService(workingVoices, validationData)
    return
  }

  // Leer archivo actual
  let serviceContent = fs.readFileSync(servicePath, 'utf8')

  // Actualizar método getAvailableVoices
  const newGetAvailableVoices = `
  async getAvailableVoices() {
    try {
      // Voces validadas y funcionando
      const validatedVoices = [
        ${workingVoices.map(voice => `'${voice}'`).join(',\n        ')}
      ]

      return {
        voices: validatedVoices.map(voice => ({
          id: voice,
          name: voice,
          gender: this.getVoiceGender(voice),
          language: 'es-ES'
        })),
        total: validatedVoices.length
      }
    } catch (error) {
      this.logger.error('Error getting available voices:', error)
      throw error
    }
  }

  getVoiceGender(voiceId) {
    const femaleVoices = ['Dalia', 'Elvira', 'Irene', 'Triana', 'Ximena DragonHD']
    const maleVoices = ['Elias', 'Jorge', 'Saul']

    if (femaleVoices.includes(voiceId)) return 'female'
    if (maleVoices.includes(voiceId)) return 'male'
    return 'unknown'
  }`

  // Reemplazar método existente o añadirlo
  if (serviceContent.includes('async getAvailableVoices()')) {
    serviceContent = serviceContent.replace(
      /async getAvailableVoices\(\)[^}]*}/s,
      newGetAvailableVoices.trim()
    )
  } else {
    // Añadir antes del último }
    serviceContent = serviceContent.replace(
      /}\s*$/,
      `\n${newGetAvailableVoices}\n}`
    )
  }

  // Actualizar método generateSpeech para usar formato correcto
  const newGenerateSpeech = `
  async generateSpeech(text, options = {}) {
    try {
      const voiceId = options.voiceId || this.config.defaultVoice || '${workingVoices[0]}'

      // Validar que la voz esté disponible
      const validatedVoices = [${workingVoices.map(v => `'${v}'`).join(', ')}]
      if (!validatedVoices.includes(voiceId)) {
        throw new Error(\`Voice "\${voiceId}" not available. Available voices: \${validatedVoices.join(', ')}\`)
      }

      const payload = {
        input_text: text,
        voice_params: {
          voice_name: voiceId
        }
      }

      this.logger.debug('TTS Request:', { payload, url: this.config.apiUrl })

      const response = await this.axiosInstance.post('/t2s', payload, {
        responseType: 'arraybuffer',
        timeout: this.config.timeout || 15000
      })

      if (!response.data || response.data.byteLength === 0) {
        throw new Error('Empty audio response')
      }

      this.logger.debug('TTS Response:', {
        audioSize: response.data.byteLength,
        contentType: response.headers['content-type']
      })

      return {
        audio: response.data,
        format: 'mp3',
        voiceId,
        size: response.data.byteLength
      }

    } catch (error) {
      this.logger.error('TTS generation failed:', error)
      throw error
    }
  }`

  if (serviceContent.includes('async generateSpeech(')) {
    serviceContent = serviceContent.replace(
      /async generateSpeech\([^}]*}[^}]*}/s,
      newGenerateSpeech.trim()
    )
  }

  fs.writeFileSync(servicePath, serviceContent)
  console.log('   ✅ CustomTTSService actualizado')
}

async function createCustomTTSService(workingVoices, validationData) {
  const servicePath = path.join(__dirname, '../core/services/tts/CustomTTSService.js')
  const serviceDir = path.dirname(servicePath)

  if (!fs.existsSync(serviceDir)) {
    fs.mkdirSync(serviceDir, { recursive: true })
  }

  const serviceContent = `// core/services/tts/CustomTTSService.js
// Auto-generado por update-tts-service.js
const axios = require('axios')

class CustomTTSService {
  constructor(config, logger) {
    this.config = config
    this.logger = logger

    this.axiosInstance = axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        'Authorization': \`Bearer \${this.config.apiKey}\`,
        'Content-Type': 'application/json'
      },
      timeout: this.config.timeout || 30000
    })
  }

  async testConnection() {
    try {
      const response = await this.axiosInstance.post('/available_voices', {}, {
        timeout: 5000
      })
      return { healthy: true, response: response.data }
    } catch (error) {
      this.logger.error('CustomTTS connection test failed:', error)
      return { healthy: false, error: error.message }
    }
  }

  async getAvailableVoices() {
    try {
      // Voces validadas y funcionando
      const validatedVoices = [
        ${workingVoices.map(voice => `'${voice}'`).join(',\n        ')}
      ]

      return {
        voices: validatedVoices.map(voice => ({
          id: voice,
          name: voice,
          gender: this.getVoiceGender(voice),
          language: 'es-ES'
        })),
        total: validatedVoices.length
      }
    } catch (error) {
      this.logger.error('Error getting available voices:', error)
      throw error
    }
  }

  getVoiceGender(voiceId) {
    const femaleVoices = ['Dalia', 'Elvira', 'Irene', 'Triana', 'Ximena DragonHD']
    const maleVoices = ['Elias', 'Jorge', 'Saul']

    if (femaleVoices.includes(voiceId)) return 'female'
    if (maleVoices.includes(voiceId)) return 'male'
    return 'unknown'
  }

  async generateSpeech(text, options = {}) {
    try {
      const voiceId = options.voiceId || this.config.defaultVoice || '${workingVoices[0]}'

      // Validar que la voz esté disponible
      const validatedVoices = [${workingVoices.map(v => `'${v}'`).join(', ')}]
      if (!validatedVoices.includes(voiceId)) {
        throw new Error(\`Voice "\${voiceId}" not available. Available voices: \${validatedVoices.join(', ')}\`)
      }

      const payload = {
        input_text: text,
        voice_params: {
          voice_name: voiceId
        }
      }

      this.logger.debug('TTS Request:', { payload, url: this.config.apiUrl })

      const response = await this.axiosInstance.post('/t2s', payload, {
        responseType: 'arraybuffer',
        timeout: this.config.timeout || 15000
      })

      if (!response.data || response.data.byteLength === 0) {
        throw new Error('Empty audio response')
      }

      this.logger.debug('TTS Response:', {
        audioSize: response.data.byteLength,
        contentType: response.headers['content-type']
      })

      return {
        audio: response.data,
        format: 'mp3',
        voiceId,
        size: response.data.byteLength
      }

    } catch (error) {
      this.logger.error('TTS generation failed:', error)
      throw error
    }
  }
}

module.exports = CustomTTSService`

  fs.writeFileSync(servicePath, serviceContent)
  console.log('   ✅ CustomTTSService creado')
}

async function updateEnvironmentConfig(workingVoices, validationData) {
  console.log('📝 Actualizando configuración de entorno...')

  const envPath = path.join(__dirname, '../core/config/environment.js')

  if (!fs.existsSync(envPath)) {
    console.log('   ⚠️  environment.js no encontrado')
    return
  }

  let envContent = fs.readFileSync(envPath, 'utf8')

  // Actualizar defaultVoice
  const defaultVoice = workingVoices.includes('Elvira') ? 'Elvira' : workingVoices[0]

  if (envContent.includes('defaultVoice:')) {
    envContent = envContent.replace(
      /defaultVoice:\s*[^,\n]+/,
      `defaultVoice: process.env.SPEECH_DEFAULT_VOICE || '${defaultVoice}'`
    )
  }

  fs.writeFileSync(envPath, envContent)
  console.log(`   ✅ Voz por defecto actualizada a: ${defaultVoice}`)
}

async function createVoicesConfig(workingVoices, validationData) {
  console.log('📝 Creando configuración de voces...')

  const configPath = path.join(__dirname, '../config/validated-voices.json')
  const configDir = path.dirname(configPath)

  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true })
  }

  const voicesConfig = {
    timestamp: new Date().toISOString(),
    validated: true,
    voices: {
      all: workingVoices,
      female: workingVoices.filter(v => ['Dalia', 'Elvira', 'Irene', 'Triana', 'Ximena DragonHD'].includes(v)),
      male: workingVoices.filter(v => ['Elias', 'Jorge', 'Saul'].includes(v))
    },
    defaultVoice: workingVoices.includes('Elvira') ? 'Elvira' : workingVoices[0],
    payloadFormat: {
      input_text: "string (required)",
      voice_id: "string (one of validated voices)"
    },
    testResults: validationData.testResults.details
  }

  fs.writeFileSync(configPath, JSON.stringify(voicesConfig, null, 2))
  console.log(`   ✅ Configuración guardada en: ${configPath}`)
}

updateTTSService().catch(console.error)
