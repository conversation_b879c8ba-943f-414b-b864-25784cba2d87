# MHC Voice Recommendator Backend - Azure Version

<h4 align="center">Backend service for a voice-based AI recommendation system powered by Azure services</h4>

## 🚀 Overview

Esta es una versión independiente del backend que utiliza los servicios de Azure:
- **Azure Speech Services** para Speech-to-Text
- **Azure OpenAI** para generación de respuestas con GPT-4
- **Azure Speech Services** para Text-to-Speech
- **Azure Container Apps** para despliegue

## 📋 Prerequisites

- [Node.js](https://nodejs.org/) (v18 or higher)
- [Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
- [Docker](https://www.docker.com/) (for containerization)
- [Visual Studio Code](https://code.visualstudio.com/)
- [Yarn](https://classic.yarnpkg.com/lang/en/docs/install/)

## 🎯 Azure Services Setup

### 1. Azure Speech Services

```bash
# Create Speech Service
az cognitiveservices account create \
  --name "your-speech-service" \
  --resource-group "your-resource-group" \
  --kind "SpeechServices" \
  --sku "S0" \
  --location "westeurope"

# Get the key
az cognitiveservices account keys list \
  --name "your-speech-service" \
  --resource-group "your-resource-group"
```

### 2. Azure OpenAI

```bash
# Create OpenAI resource
az cognitiveservices account create \
  --name "your-openai-resource" \
  --resource-group "your-resource-group" \
  --kind "OpenAI" \
  --sku "S0" \
  --location "westeurope"

# Deploy GPT-4 model
az cognitiveservices account deployment create \
  --name "your-openai-resource" \
  --resource-group "your-resource-group" \
  --deployment-name "gpt-4" \
  --model-name "gpt-4" \
  --model-version "0125-Preview" \
  --model-format "OpenAI" \
  --sku-capacity "10" \
  --sku-name "Standard"
```

## 🛠️ Development Setup

1. **Clone and setup:**
```bash
git clone [repository-url]
cd recomendador-voz-azure-backend
yarn install
```

2. **Environment configuration:**
```bash
cp .env.template .env
# Edit .env with your Azure credentials
```

3. **TTS not generating audio:**
   - Check TTS voice name is correct for your region
   - Verify text length (max 5000 characters)
   - Ensure proper SSML format

4. **Container deployment issues:**
   - Check resource limits (CPU/Memory)
   - Verify all environment variables are set
   - Review container logs: `az containerapp logs show`

### Debug Commands

```bash
# Test Azure Speech Service
az cognitiveservices account show \
  --name "your-speech-service" \
  --resource-group "your-resource-group"

# Test Azure OpenAI deployment
az cognitiveservices account deployment list \
  --name "your-openai-resource" \
  --resource-group "your-resource-group"

# Check container app logs
az containerapp logs show \
  --name "azure-voice-backend" \
  --resource-group "voice-app-rg" \
  --follow
```

## 💰 Cost Optimization

### Azure Speech Services
- **Speech-to-Text**: ~$1 per hour of audio
- **Text-to-Speech**: ~$4 per 1M characters
- **Tip**: Use Standard tier for production, Free tier for development

### Azure OpenAI
- **GPT-4**: ~$0.03 per 1K input tokens, $0.06 per 1K output tokens
- **Tip**: Monitor usage in Azure portal, set spending limits

### Container Apps
- **Consumption**: Pay only when requests are processed
- **Scale to zero**: Automatically scales down to 0 when idle
- **Tip**: Use minimum replicas of 1 for faster cold starts

## 🔒 Security Best Practices

1. **API Keys Management:**
```bash
# Store secrets in Azure Key Vault
az keyvault create --name "voice-app-kv" \
  --resource-group "voice-app-rg" \
  --location "westeurope"

# Add secrets
az keyvault secret set --vault-name "voice-app-kv" \
  --name "azure-speech-key" --value "your_key"
```

2. **Network Security:**
```bash
# Restrict Container App ingress
az containerapp ingress enable \
  --name "azure-voice-backend" \
  --resource-group "voice-app-rg" \
  --type external \
  --allow-insecure false \
  --traffic-weight latest=100
```

3. **CORS Configuration:**
   - Update `FRONTEND_DEV_URL` and `FRONTEND_PRO_URL` in environment
   - Restrict to your actual frontend domains

## 🚦 Testing

### Unit Tests
```bash
# Run basic health checks
npm run test:health

# Test individual services
npm run test:speech
npm run test:openai
npm run test:tts
```

### Load Testing
```bash
# Install Artillery for load testing
npm install -g artillery

# Run load test
artillery run load-test.yml
```

## 📈 Performance Tuning

### Speech Recognition
- Use `enableDictation: true` for better punctuation
- Set appropriate `profanity` filter for your use case
- Consider `enableDiarization: true` for multi-speaker scenarios

### OpenAI Optimization
- Adjust `max_tokens` based on your needs
- Fine-tune `temperature` for consistent responses
- Use `frequency_penalty` to reduce repetition

### TTS Optimization
- Cache frequently used audio responses
- Use appropriate `speakingRate` and `pitch` for natural speech
- Consider batch processing for multiple texts

## 🔄 Migration from Google Version

Si ya tienes la versión de Google funcionando:

1. **Mantén ambas versiones:** Este código es completamente independiente
2. **Compara respuestas:** Usa ambos sistemas en paralelo para comparar
3. **Gradual rollout:** Migra usuarios gradualmente usando feature flags

### Key Differences

| Feature | Google | Azure |
|---------|--------|-------|
| STT Accuracy | Excellent | Very Good + Customizable |
| TTS Quality | Superior | Good + More voices |
| AI Models | Gemini 2.0 | GPT-4 |
| Cost | Lower | Higher but more features |
| Integration | GCP ecosystem | Microsoft ecosystem |

## 📞 Support

- **Azure Documentation**: [Azure Speech Services](https://docs.microsoft.com/azure/cognitive-services/speech-service/)
- **OpenAI on Azure**: [Azure OpenAI Service](https://docs.microsoft.com/azure/cognitive-services/openai/)
- **Container Apps**: [Azure Container Apps Documentation](https://docs.microsoft.com/azure/container-apps/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/azure-enhancement`
3. Make your changes
4. Test with both development and production configs
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**⚡ Quick Start Checklist:**

- [ ] Azure Speech Service created and key obtained
- [ ] Azure OpenAI resource created with GPT-4 deployment
- [ ] Environment variables configured in `.env`
- [ ] Dependencies installed with `yarn install`
- [ ] Development server running with `yarn dev`
- [ ] Health check passing at `/api/health`
- [ ] WebSocket connection working with frontend
- [ ] Speech recognition and TTS working end-to-end

**🎯 Production Deployment Checklist:**

- [ ] Container Registry created and configured
- [ ] Secrets stored in Azure Key Vault
- [ ] Container Apps environment set up
- [ ] Application deployed and accessible
- [ ] Health checks configured and passing
- [ ] Monitoring and logging enabled
- [ ] CORS properly configured for production domains
- [ ] Load testing completed
- [ ] Backup and disaster recovery plan in place. **Required environment variables:**
```env
AZURE_SPEECH_KEY="your_azure_speech_key"
AZURE_SPEECH_REGION="westeurope"
AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com"
AZURE_OPENAI_KEY="your_azure_openai_key"
AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4"
API_KEY="your_secure_api_key"
```

4. **Run development server:**
```bash
yarn dev
```

## 🐳 Docker Development

```bash
# Build image
docker build -t azure-voice-backend .

# Run container
docker run -p 3001:8080 --env-file .env azure-voice-backend
```

## ☁️ Azure Deployment

### Method 1: Azure Container Apps (Recommended)

```bash
# Login to Azure
az login

# Create resource group
az group create --name "voice-app-rg" --location "westeurope"

# Create Container Apps environment
az containerapp env create \
  --name "voice-app-env" \
  --resource-group "voice-app-rg" \
  --location "westeurope"

# Deploy the app
az containerapp create \
  --name "azure-voice-backend" \
  --resource-group "voice-app-rg" \
  --environment "voice-app-env" \
  --image "your-registry.azurecr.io/azure-voice-backend:latest" \
  --target-port 8080 \
  --ingress external \
  --min-replicas 1 \
  --max-replicas 10 \
  --cpu 0.5 \
  --memory 1Gi \
  --env-vars \
    AZURE_SPEECH_KEY="secretref:azure-speech-key" \
    AZURE_OPENAI_KEY="secretref:azure-openai-key" \
    NODE_ENV="production"
```

### Method 2: Azure Container Registry + Azure Container Instances

```bash
# Create Azure Container Registry
az acr create --resource-group "voice-app-rg" \
  --name "yourregistry" --sku Basic

# Build and push image
az acr build --registry yourregistry \
  --image azure-voice-backend:latest .

# Deploy to Container Instances
az container create \
  --resource-group "voice-app-rg" \
  --name "azure-voice-backend" \
  --image "yourregistry.azurecr.io/azure-voice-backend:latest" \
  --cpu 1 \
  --memory 1 \
  --registry-login-server "yourregistry.azurecr.io" \
  --ports 8080 \
  --environment-variables \
    NODE_ENV="production" \
  --secure-environment-variables \
    AZURE_SPEECH_KEY="your_key" \
    AZURE_OPENAI_KEY="your_openai_key"
```

## 🔧 Configuration Options

### Speech Recognition Settings

```javascript
// En config.js
AZURE_SPEECH_CONFIG: {
  region: 'westeurope',           // Azure region
  language: 'es-ES',              // Language code
  format: 'Detailed',             // Simple or Detailed
  profanity: 'Masked',            // Masked, Removed, Raw
  enableDictation: true,          // Better punctuation
  enableWordLevelTimestamps: true // Timing info
}
```

### Text-to-Speech Settings

```javascript
AZURE_TTS_CONFIG: {
  voice: 'es-ES-ElviraNeural',    // Spanish neural voice
  outputFormat: 'Audio16Khz32KBitRateMonoMp3',
  speakingRate: '0%',             // -50% to +200%
  pitch: '0%'                     // -50% to +50%
}
```

### Available Spanish Voices

- `es-ES-ElviraNeural` (Female) - Recommended
- `es-ES-AlvaroNeural` (Male)
- `es-ES-AbrilNeural` (Female)
- `es-ES-ArnauNeural` (Male)

## 📊 Monitoring & Health Checks

```bash
# Health check endpoint
curl http://localhost:3001/api/health

# Response example:
{
  "status": "ok",
  "timestamp": "2025-01-01T12:00:00.000Z",
  "services": {
    "openai": {
      "isInitialized": true,
      "endpoint": "https://your-resource.openai.azure.com",
      "deploymentName": "gpt-4"
    },
    "tts": {
      "isInitialized": true,
      "voice": "es-ES-ElviraNeural",
      "region": "westeurope"
    }
  }
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Speech recognition not working:**
   - Verify `AZURE_SPEECH_KEY` and `AZURE_SPEECH_REGION`
   - Check microphone permissions in browser
   - Ensure audio format is supported (16kHz, mono, linear PCM)

2. **OpenAI errors:**
   - Verify deployment name matches your Azure OpenAI deployment
   - Check quota limits in Azure portal
   - Ensure API version is compatible

3