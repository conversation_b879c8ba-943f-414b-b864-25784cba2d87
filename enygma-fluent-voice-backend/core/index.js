/**
 * Core module main export
 * Provides centralized access to all core functionality
 */

// Configuration
const { environment } = require('./config/environment')
const { securityManager } = require('./config/security')

// Utilities
const utils = require('./utils')

// Types (for JSDoc and runtime validation)
const VoiceTypes = require('./types/Voice')

module.exports = {
  // Configuration
  environment,
  security: securityManager,

  // Utilities (direct access)
  utils,

  // Individual utility access for convenience
  logger: utils.logger,
  validation: utils.validation,
  performance: utils.performance,
  helpers: utils.helpers,

  // Types and validation
  types: VoiceTypes,

  // Core initialization function
  async init(options = {}) {
    const { validateConfig = true, startMonitoring = true } = options

    utils.logger.info('🚀 Initializing core modules...')

    try {
      // Validate environment configuration
      if (validateConfig) {
        utils.logger.debug('🔍 Validating environment configuration...')
        const configSummary = environment.getSummary()
        utils.logger.info('📋 Environment configuration loaded:', configSummary)
      }

      // Start performance monitoring
      if (startMonitoring && environment.performance.enableMetrics) {
        utils.logger.debug('📈 Starting performance monitoring...')
        utils.performance.startTimer('core_initialization')
      }

      // Log initialization success
      utils.logger.info('✅ Core modules initialized successfully')

      if (startMonitoring && environment.performance.enableMetrics) {
        utils.performance.endTimer('core_initialization')
      }

      return {
        success: true,
        environment: environment.nodeEnv,
        providers: environment.providers.enabled,
        security: securityManager.getSecurityStatus()
      }

    } catch (error) {
      utils.logger.error('❌ Core initialization failed:', error)
      throw new Error(`Core initialization failed: ${error.message}`)
    }
  },

  // Health check function
  getHealthStatus() {
    try {
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: environment.nodeEnv,
        uptime: process.uptime(),
        memory: utils.performance.getMemoryUsage(),
        providers: {
          default: environment.providers.default,
          enabled: environment.providers.enabled
        },
        security: securityManager.getSecurityStatus(),
        performance: utils.performance.getLogStats()
      }
    } catch (error) {
      utils.logger.error('Health check failed:', error)
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      }
    }
  },

  // Configuration export for easy access
  getConfig() {
    return {
      server: environment.server,
      security: environment.security,
      providers: environment.providers,
      google: environment.google,
      azure: environment.azure,
      ai: environment.ai,
      performance: environment.performance,
      monitoring: environment.monitoring
    }
  },

  // Cleanup function
  cleanup() {
    utils.logger.info('🧹 Cleaning up core modules...')

    try {
      // Cleanup performance monitoring
      if (utils.performance.cleanup) {
        utils.performance.cleanup()
      }

      // Rotate logs if needed
      if (utils.logger.rotateLogFiles) {
        utils.logger.rotateLogFiles()
      }

      utils.logger.info('✅ Core cleanup completed')
    } catch (error) {
      utils.logger.error('❌ Core cleanup failed:', error)
    }
  }
}
