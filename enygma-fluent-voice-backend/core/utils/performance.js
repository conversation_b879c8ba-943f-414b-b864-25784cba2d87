/**
 * Performance monitoring and optimization utilities
 * Tracks performance metrics, memory usage, and provides optimization recommendations
 */
const { logger } = require('./logger')
const { environment } = require('../config/environment')

class PerformanceMonitor {
  constructor() {
    this.config = environment.performance
    this.metrics = new Map()
    this.timers = new Map()
    this.memoryBaseline = process.memoryUsage()
    this.startTime = Date.now()

    // Start periodic monitoring if enabled
    if (this.config.enableMetrics) {
      this.startPeriodicMonitoring()
    }
  }

  // ===========================================
  // TIMING UTILITIES
  // ===========================================

  startTimer(operation) {
    const startTime = process.hrtime.bigint()
    this.timers.set(operation, {
      startTime,
      startTimestamp: Date.now()
    })

    logger.trace(`⏱️ Timer started: ${operation}`)
    return operation
  }

  endTimer(operation) {
    const timer = this.timers.get(operation)
    if (!timer) {
      logger.warn(`⚠️ Timer not found: ${operation}`)
      return null
    }

    const endTime = process.hrtime.bigint()
    const duration = Number(endTime - timer.startTime) / 1000000 // Convert to milliseconds

    this.timers.delete(operation)
    this.recordMetric(operation, duration)

    logger.debug(`⚡ ${operation}: ${duration.toFixed(2)}ms`)
    return duration
  }

  // Time synchronous operations
  timeSync(operation, fn) {
    this.startTimer(operation)
    try {
      const result = fn()
      this.endTimer(operation)
      return result
    } catch (error) {
      this.endTimer(operation)
      this.recordError(operation, error)
      throw error
    }
  }

  // Time asynchronous operations
  async timeAsync(operation, asyncFn) {
    this.startTimer(operation)
    try {
      const result = await asyncFn()
      this.endTimer(operation)
      return result
    } catch (error) {
      this.endTimer(operation)
      this.recordError(operation, error)
      throw error
    }
  }

  // ===========================================
  // METRICS COLLECTION
  // ===========================================

  recordMetric(operation, value, metadata = {}) {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, {
        operation,
        count: 0,
        totalTime: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0,
        errors: 0,
        lastExecuted: null,
        metadata: {}
      })
    }

    const metric = this.metrics.get(operation)
    metric.count++
    metric.totalTime += value
    metric.avgTime = metric.totalTime / metric.count
    metric.minTime = Math.min(metric.minTime, value)
    metric.maxTime = Math.max(metric.maxTime, value)
    metric.lastExecuted = new Date()
    metric.metadata = { ...metric.metadata, ...metadata }

    // Log slow operations
    const slowThreshold = this.getSlowThreshold(operation)
    if (value > slowThreshold) {
      logger.warn(`🐌 Slow operation detected: ${operation} took ${value.toFixed(2)}ms (threshold: ${slowThreshold}ms)`)
    }
  }

  recordError(operation, error) {
    if (this.metrics.has(operation)) {
      this.metrics.get(operation).errors++
    }

    logger.error(`❌ Error in ${operation}:`, {
      error: error.message,
      stack: error.stack
    })
  }

  getSlowThreshold(operation) {
    const thresholds = {
      'speech_recognition': 2000,
      'ai_generation': 5000,
      'tts_generation': 3000,
      'audio_processing': 1000,
      'default': 1000
    }

    return thresholds[operation] || thresholds.default
  }

  // ===========================================
  // MEMORY MONITORING
  // ===========================================

  getMemoryUsage() {
    const usage = process.memoryUsage()
    const baseline = this.memoryBaseline

    return {
      current: {
        rss: this.formatBytes(usage.rss),
        heapTotal: this.formatBytes(usage.heapTotal),
        heapUsed: this.formatBytes(usage.heapUsed),
        external: this.formatBytes(usage.external),
        arrayBuffers: this.formatBytes(usage.arrayBuffers)
      },
      baseline: {
        rss: this.formatBytes(baseline.rss),
        heapTotal: this.formatBytes(baseline.heapTotal),
        heapUsed: this.formatBytes(baseline.heapUsed),
        external: this.formatBytes(baseline.external),
        arrayBuffers: this.formatBytes(baseline.arrayBuffers)
      },
      growth: {
        rss: this.formatBytes(usage.rss - baseline.rss),
        heapTotal: this.formatBytes(usage.heapTotal - baseline.heapTotal),
        heapUsed: this.formatBytes(usage.heapUsed - baseline.heapUsed),
        external: this.formatBytes(usage.external - baseline.external),
        arrayBuffers: this.formatBytes(usage.arrayBuffers - baseline.arrayBuffers)
      }
    }
  }

  checkMemoryLeak() {
    const usage = process.memoryUsage()
    const heapGrowth = usage.heapUsed - this.memoryBaseline.heapUsed
    const rssGrowth = usage.rss - this.memoryBaseline.rss

    const warnings = []

    // Check for excessive heap growth (> 100MB)
    if (heapGrowth > 100 * 1024 * 1024) {
      warnings.push(`Heap memory grew by ${this.formatBytes(heapGrowth)}`)
    }

    // Check for excessive RSS growth (> 200MB)
    if (rssGrowth > 200 * 1024 * 1024) {
      warnings.push(`RSS memory grew by ${this.formatBytes(rssGrowth)}`)
    }

    if (warnings.length > 0) {
      logger.warn('🔍 Potential memory leak detected:', {
        warnings,
        memoryUsage: this.getMemoryUsage()
      })
    }

    return warnings.length === 0
  }

  forceGarbageCollection() {
    if (global.gc) {
      const beforeGC = process.memoryUsage()
      global.gc()
      const afterGC = process.memoryUsage()

      const freed = beforeGC.heapUsed - afterGC.heapUsed
      logger.info(`🗑️ Garbage collection freed ${this.formatBytes(freed)}`)

      return freed
    } else {
      logger.warn('⚠️ Garbage collection not available (run with --expose-gc)')
      return 0
    }
  }

  // ===========================================
  // CPU MONITORING
  // ===========================================

  getCPUUsage() {
    const usage = process.cpuUsage()
    const uptime = process.uptime() * 1000000 // Convert to microseconds

    return {
      user: Math.round((usage.user / uptime) * 100 * 100) / 100, // Percentage
      system: Math.round((usage.system / uptime) * 100 * 100) / 100, // Percentage
      total: Math.round(((usage.user + usage.system) / uptime) * 100 * 100) / 100
    }
  }

  // ===========================================
  // OPERATION PROFILING
  // ===========================================

  profileOperation(operation, iterations = 1000) {
    logger.info(`🔬 Profiling operation: ${operation} (${iterations} iterations)`)

    const results = []
    const startTime = Date.now()

    for (let i = 0; i < iterations; i++) {
      const iterationStart = process.hrtime.bigint()
      // Operation would be executed here
      const iterationEnd = process.hrtime.bigint()
      const duration = Number(iterationEnd - iterationStart) / 1000000
      results.push(duration)
    }

    const totalTime = Date.now() - startTime
    const sortedResults = results.sort((a, b) => a - b)

    const profile = {
      operation,
      iterations,
      totalTime,
      avgTime: results.reduce((a, b) => a + b, 0) / results.length,
      minTime: sortedResults[0],
      maxTime: sortedResults[sortedResults.length - 1],
      medianTime: sortedResults[Math.floor(sortedResults.length / 2)],
      p95Time: sortedResults[Math.floor(sortedResults.length * 0.95)],
      p99Time: sortedResults[Math.floor(sortedResults.length * 0.99)],
      throughput: (iterations / totalTime) * 1000 // Operations per second
    }

    logger.info(`📊 Profile results for ${operation}:`, profile)
    return profile
  }

  // ===========================================
  // PERFORMANCE REPORTS
  // ===========================================

  generateReport() {
    const uptime = Date.now() - this.startTime
    const memoryUsage = this.getMemoryUsage()
    const cpuUsage = this.getCPUUsage()

    const report = {
      timestamp: new Date().toISOString(),
      uptime: this.formatDuration(uptime),
      memory: memoryUsage,
      cpu: cpuUsage,
      metrics: this.getMetricsSummary(),
      recommendations: this.getOptimizationRecommendations()
    }

    return report
  }

  getMetricsSummary() {
    const summary = {
      totalOperations: 0,
      totalErrors: 0,
      averageResponseTime: 0,
      slowestOperation: null,
      fastestOperation: null
    }

    let totalTime = 0
    let minAvgTime = Infinity
    let maxAvgTime = 0

    for (const [operation, metric] of this.metrics.entries()) {
      summary.totalOperations += metric.count
      summary.totalErrors += metric.errors
      totalTime += metric.totalTime

      if (metric.avgTime < minAvgTime) {
        minAvgTime = metric.avgTime
        summary.fastestOperation = operation
      }

      if (metric.avgTime > maxAvgTime) {
        maxAvgTime = metric.avgTime
        summary.slowestOperation = operation
      }
    }

    summary.averageResponseTime = summary.totalOperations > 0 ?
      totalTime / summary.totalOperations : 0

    return summary
  }

  getOptimizationRecommendations() {
    const recommendations = []
    const memoryUsage = process.memoryUsage()

    // Memory recommendations
    if (memoryUsage.heapUsed > 500 * 1024 * 1024) { // > 500MB
      recommendations.push({
        type: 'memory',
        priority: 'high',
        message: 'High heap memory usage detected. Consider implementing memory cleanup.',
        action: 'Review object retention and implement garbage collection triggers'
      })
    }

    // Performance recommendations
    for (const [operation, metric] of this.metrics.entries()) {
      const errorRate = metric.errors / metric.count

      if (errorRate > 0.1) { // > 10% error rate
        recommendations.push({
          type: 'reliability',
          priority: 'high',
          message: `High error rate for ${operation}: ${(errorRate * 100).toFixed(1)}%`,
          action: 'Investigate and fix underlying issues'
        })
      }

      if (metric.avgTime > this.getSlowThreshold(operation)) {
        recommendations.push({
          type: 'performance',
          priority: 'medium',
          message: `${operation} is slower than expected (${metric.avgTime.toFixed(2)}ms avg)`,
          action: 'Profile and optimize the operation'
        })
      }
    }

    return recommendations
  }

  // ===========================================
  // PERIODIC MONITORING
  // ===========================================

  startPeriodicMonitoring() {
    const interval = 60000 // 1 minute

    setInterval(() => {
      this.checkMemoryLeak()

      if (this.config.enableDetailedLogs) {
        const report = this.generateReport()
        logger.debug('📊 Performance report:', report)
      }
    }, interval)

    logger.info('📈 Periodic performance monitoring started')
  }

  // ===========================================
  // UTILITY METHODS
  // ===========================================

  formatBytes(bytes) {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  }

  // ===========================================
  // BENCHMARKING
  // ===========================================

  async benchmark(name, fn, options = {}) {
    const {
      iterations = 100,
      warmup = 10,
      timeout = 30000
    } = options

    logger.info(`🏃 Starting benchmark: ${name}`)

    // Warmup phase
    for (let i = 0; i < warmup; i++) {
      try {
        await fn()
      } catch (error) {
        logger.warn(`Warmup iteration ${i} failed:`, error.message)
      }
    }

    // Benchmark phase
    const results = []
    const startTime = Date.now()

    for (let i = 0; i < iterations; i++) {
      if (Date.now() - startTime > timeout) {
        logger.warn(`Benchmark ${name} timed out after ${i} iterations`)
        break
      }

      const iterationStart = process.hrtime.bigint()
      try {
        await fn()
        const iterationEnd = process.hrtime.bigint()
        results.push(Number(iterationEnd - iterationStart) / 1000000)
      } catch (error) {
        logger.error(`Benchmark iteration ${i} failed:`, error.message)
      }
    }

    const stats = this.calculateStats(results)
    logger.info(`📊 Benchmark ${name} completed:`, stats)

    return stats
  }

  calculateStats(values) {
    if (values.length === 0) return null

    const sorted = [...values].sort((a, b) => a - b)
    const sum = values.reduce((a, b) => a + b, 0)

    return {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      mean: sum / values.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      stdDev: this.calculateStdDev(values, sum / values.length)
    }
  }

  calculateStdDev(values, mean) {
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length
    return Math.sqrt(variance)
  }

  // ===========================================
  // ALERTS & THRESHOLDS
  // ===========================================

  setThreshold(metric, threshold, callback) {
    if (!this.thresholds) this.thresholds = new Map()

    this.thresholds.set(metric, { threshold, callback })
    logger.debug(`🚨 Threshold set for ${metric}: ${threshold}`)
  }

  checkThresholds() {
    if (!this.thresholds) return

    for (const [metricName, { threshold, callback }] of this.thresholds) {
      const metric = this.metrics.get(metricName)
      if (metric && metric.avgTime > threshold) {
        callback(metric, threshold)
      }
    }
  }

  // ===========================================
  // EXPORT & CLEANUP
  // ===========================================

  exportMetrics() {
    const data = {
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      metrics: Object.fromEntries(this.metrics),
      memory: this.getMemoryUsage(),
      cpu: this.getCPUUsage()
    }

    return JSON.stringify(data, null, 2)
  }

  reset() {
    this.metrics.clear()
    this.timers.clear()
    this.memoryBaseline = process.memoryUsage()
    this.startTime = Date.now()

    logger.info('🔄 Performance metrics reset')
  }

  cleanup() {
    this.reset()
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
    }
    logger.info('🧹 Performance monitor cleaned up')
  }
}

// Create singleton instance
const performance = new PerformanceMonitor()

// Export convenience functions
module.exports = {
  PerformanceMonitor,
  performance,

  // Convenience functions
  startTimer: (operation) => performance.startTimer(operation),
  endTimer: (operation) => performance.endTimer(operation),
  timeSync: (operation, fn) => performance.timeSync(operation, fn),
  timeAsync: (operation, fn) => performance.timeAsync(operation, fn),
  recordMetric: (operation, value, metadata) => performance.recordMetric(operation, value, metadata),
  getReport: () => performance.generateReport(),
  benchmark: (name, fn, options) => performance.benchmark(name, fn, options)
}
