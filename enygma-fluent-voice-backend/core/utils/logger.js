/**
 * Advanced logging system with multiple levels, formatting, and file output
 * Supports structured logging and performance monitoring
 */
const fs = require('fs')
const path = require('path')
const { environment } = require('../config/environment')

class Logger {
  constructor() {
    this.config = environment.monitoring
    this.logLevel = this.getLogLevel()
    this.colors = this.getColors()

    // Ensure log directory exists if file logging is enabled
    if (this.config.logToFile) {
      this.ensureLogDirectory()
    }

    // Log levels hierarchy
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    }

    // Performance tracking
    this.performanceTimers = new Map()
  }

  // ===========================================
  // LOG LEVEL MANAGEMENT
  // ===========================================

  getLogLevel() {
    const envLevel = environment.server.logLevel?.toLowerCase()
    const validLevels = ['error', 'warn', 'info', 'debug', 'trace']
    return validLevels.includes(envLevel) ? envLevel : 'info'
  }

  setLogLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.logLevel = level
      this.info(`📊 Log level changed to: ${level}`)
    } else {
      this.warn(`⚠️ Invalid log level: ${level}`)
    }
  }

  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel]
  }

  // ===========================================
  // COLOR CONFIGURATION
  // ===========================================

  getColors() {
    return {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      dim: '\x1b[2m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
      gray: '\x1b[90m'
    }
  }

  colorize(text, color) {
    if (!process.stdout.isTTY || environment.isProduction) {
      return text // No colors in production or non-TTY
    }
    return `${this.colors[color]}${text}${this.colors.reset}`
  }

  // ===========================================
  // CORE LOGGING METHODS
  // ===========================================

  error(message, meta = {}) {
    if (this.shouldLog('error')) {
      this.log('error', message, meta)
    }
  }

  warn(message, meta = {}) {
    if (this.shouldLog('warn')) {
      this.log('warn', message, meta)
    }
  }

  info(message, meta = {}) {
    if (this.shouldLog('info')) {
      this.log('info', message, meta)
    }
  }

  debug(message, meta = {}) {
    if (this.shouldLog('debug')) {
      this.log('debug', message, meta)
    }
  }

  trace(message, meta = {}) {
    if (this.shouldLog('trace')) {
      this.log('trace', message, meta)
    }
  }

  // ===========================================
  // MAIN LOG METHOD
  // ===========================================

  log(level, message, meta = {}) {
    const timestamp = new Date().toISOString()
    const logEntry = this.formatLogEntry(level, message, meta, timestamp)

    // Console output
    this.outputToConsole(level, logEntry)

    // File output (if enabled)
    if (this.config.logToFile) {
      this.outputToFile(level, logEntry, timestamp)
    }
  }

  formatLogEntry(level, message, meta, timestamp) {
    const baseEntry = {
      timestamp,
      level: level.toUpperCase(),
      message: this.sanitizeMessage(message),
      pid: process.pid,
      environment: environment.nodeEnv
    }

    // Add metadata if provided
    if (Object.keys(meta).length > 0) {
      baseEntry.meta = this.sanitizeMeta(meta)
    }

    // Add stack trace for errors
    if (level === 'error' && meta instanceof Error) {
      baseEntry.stack = meta.stack
      baseEntry.errorName = meta.name
      baseEntry.errorMessage = meta.message
    }

    return baseEntry
  }

  // ===========================================
  // OUTPUT METHODS
  // ===========================================

  outputToConsole(level, logEntry) {
    const colorMap = {
      error: 'red',
      warn: 'yellow',
      info: 'cyan',
      debug: 'blue',
      trace: 'gray'
    }

    const color = colorMap[level] || 'white'
    const levelBadge = this.colorize(` ${logEntry.level} `, color)
    const timestamp = this.colorize(logEntry.timestamp, 'gray')
    const message = logEntry.message

    let output = `${timestamp} ${levelBadge} ${message}`

    // Add metadata if present and detailed logs enabled
    if (logEntry.meta && this.config.enableDetailedLogs) {
      const metaString = JSON.stringify(logEntry.meta, null, 2)
      output += `\n${this.colorize(metaString, 'dim')}`
    }

    // Add stack trace for errors
    if (logEntry.stack) {
      output += `\n${this.colorize(logEntry.stack, 'red')}`
    }

    console.log(output)
  }

  outputToFile(level, logEntry, timestamp) {
    try {
      const logFile = this.getLogFileName(level, timestamp)
      const logLine = JSON.stringify(logEntry) + '\n'

      fs.appendFileSync(logFile, logLine, 'utf8')
    } catch (error) {
      // Fallback to console if file logging fails
      console.error('Failed to write to log file:', error.message)
    }
  }

  // ===========================================
  // SPECIALIZED LOGGING METHODS
  // ===========================================

  // HTTP request logging
  logRequest(req, res, responseTime) {
    const logData = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentLength: res.get('Content-Length') || 0
    }

    if (res.statusCode >= 400) {
      this.warn(`📡 ${req.method} ${req.url} - ${res.statusCode}`, logData)
    } else {
      this.info(`📡 ${req.method} ${req.url} - ${res.statusCode}`, logData)
    }
  }

  // Socket event logging
  logSocket(event, socketId, data = {}) {
    this.debug(`🔌 Socket ${event}: ${socketId}`, data)
  }

  // Service operation logging
  logService(service, operation, data = {}) {
    this.info(`⚙️ ${service}.${operation}`, data)
  }

  // Performance logging
  logPerformance(operation, duration, metadata = {}) {
    const logData = {
      operation,
      duration: `${duration}ms`,
      ...metadata
    }

    if (duration > 5000) { // Slow operation threshold
      this.warn(`🐌 Slow operation: ${operation}`, logData)
    } else {
      this.debug(`⚡ Performance: ${operation}`, logData)
    }
  }

  // Security event logging
  logSecurity(event, details = {}) {
    this.warn(`🔒 Security event: ${event}`, details)
  }

  // ===========================================
  // PERFORMANCE MONITORING
  // ===========================================

  startTimer(operation) {
    this.performanceTimers.set(operation, Date.now())
    this.trace(`⏱️ Timer started: ${operation}`)
  }

  endTimer(operation, metadata = {}) {
    const startTime = this.performanceTimers.get(operation)
    if (startTime) {
      const duration = Date.now() - startTime
      this.performanceTimers.delete(operation)
      this.logPerformance(operation, duration, metadata)
      return duration
    } else {
      this.warn(`⚠️ Timer not found: ${operation}`)
      return null
    }
  }

  // Async operation wrapper with automatic timing
  async timeAsync(operation, asyncFn, metadata = {}) {
    this.startTimer(operation)
    try {
      const result = await asyncFn()
      this.endTimer(operation, { ...metadata, success: true })
      return result
    } catch (error) {
      this.endTimer(operation, { ...metadata, success: false, error: error.message })
      throw error
    }
  }

  // ===========================================
  // UTILITY METHODS
  // ===========================================

  sanitizeMessage(message) {
    if (typeof message === 'string') {
      return message.replace(/[\r\n\t]/g, ' ').trim()
    }
    return String(message)
  }

  sanitizeMeta(meta) {
    // Deep clone and sanitize sensitive data
    const sanitized = JSON.parse(JSON.stringify(meta))
    return this.redactSensitiveData(sanitized)
  }

  redactSensitiveData(obj) {
    const sensitiveKeys = [
      'password', 'token', 'key', 'secret', 'auth', 'authorization',
      'apikey', 'api_key', 'subscriptionkey', 'subscription_key'
    ]

    if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          obj[key] = '***REDACTED***'
        } else if (typeof obj[key] === 'object') {
          obj[key] = this.redactSensitiveData(obj[key])
        }
      }
    }

    return obj
  }

  ensureLogDirectory() {
    const logDir = this.config.logDirectory
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true })
      this.info(`📁 Created log directory: ${logDir}`)
    }
  }

  getLogFileName(level, timestamp) {
    const date = timestamp.split('T')[0] // Get YYYY-MM-DD
    const logDir = this.config.logDirectory

    // Separate error logs from others
    if (level === 'error') {
      return path.join(logDir, `error-${date}.log`)
    } else {
      return path.join(logDir, `app-${date}.log`)
    }
  }

  // ===========================================
  // LOG ROTATION & CLEANUP
  // ===========================================

  rotateLogFiles(maxAge = 7) {
    if (!this.config.logToFile) return

    try {
      const logDir = this.config.logDirectory
      const files = fs.readdirSync(logDir)
      const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000)

      files.forEach(file => {
        const filePath = path.join(logDir, file)
        const stats = fs.statSync(filePath)

        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath)
          this.info(`🗑️ Rotated old log file: ${file}`)
        }
      })
    } catch (error) {
      this.error('Failed to rotate log files:', error)
    }
  }

  // ===========================================
  // HEALTH CHECK & STATISTICS
  // ===========================================

  getLogStats() {
    const stats = {
      currentLevel: this.logLevel,
      fileLogging: this.config.logToFile,
      detailedLogs: this.config.enableDetailedLogs,
      activeTimers: this.performanceTimers.size,
      environment: environment.nodeEnv
    }

    if (this.config.logToFile) {
      try {
        const logDir = this.config.logDirectory
        const files = fs.readdirSync(logDir)
        stats.logFiles = files.length
        stats.logDirectory = logDir
      } catch (error) {
        stats.logFilesError = error.message
      }
    }

    return stats
  }

  // ===========================================
  // STRUCTURED LOGGING HELPERS
  // ===========================================

  // Create child logger with default metadata
  child(defaultMeta = {}) {
    return {
      error: (msg, meta = {}) => this.error(msg, { ...defaultMeta, ...meta }),
      warn: (msg, meta = {}) => this.warn(msg, { ...defaultMeta, ...meta }),
      info: (msg, meta = {}) => this.info(msg, { ...defaultMeta, ...meta }),
      debug: (msg, meta = {}) => this.debug(msg, { ...defaultMeta, ...meta }),
      trace: (msg, meta = {}) => this.trace(msg, { ...defaultMeta, ...meta })
    }
  }

  // Request-scoped logger
  requestLogger(req) {
    const requestId = req.headers['x-request-id'] || this.generateRequestId()
    return this.child({
      requestId,
      method: req.method,
      url: req.url,
      ip: req.ip
    })
  }

  generateRequestId() {
    return Math.random().toString(36).substring(2, 15)
  }

  // ===========================================
  // BACKWARD COMPATIBILITY
  // ===========================================

  // Legacy customLog function for backward compatibility
  customLog(...args) {
    this.info(args.join(' '))
  }
}

// Create singleton instance
const logger = new Logger()

// Start log rotation interval (once per day)
if (logger.config.logToFile) {
  setInterval(() => {
    logger.rotateLogFiles()
  }, 24 * 60 * 60 * 1000) // 24 hours
}

// Export both class and instance
module.exports = {
  Logger,
  logger,

  // Backward compatibility
  customLog: (...args) => logger.customLog(...args)
}
