/**
 * Comprehensive validation utilities for voice AI backend
 * Provides validation for audio, text, configuration, and API inputs
 */
const { logger } = require('./logger')

class ValidationSystem {
  constructor() {
    this.maxTextLength = 5000
    this.maxAudioBufferSize = 1024 * 1024 // 1MB
    this.maxConversationHistory = 50
  }

  // ===========================================
  // AUDIO VALIDATION
  // ===========================================

  validateAudioBuffer(audioBuffer, options = {}) {
    const errors = []
    const config = {
      maxSize: options.maxSize || this.maxAudioBufferSize,
      minSize: options.minSize || 1,
      allowedTypes: options.allowedTypes || ['Buffer', 'Uint8Array', 'ArrayBuffer']
    }

    // Check if audio buffer exists
    if (!audioBuffer) {
      errors.push('Audio buffer is required')
      return { valid: false, errors }
    }

    // Check buffer type
    const bufferType = this.getBufferType(audioBuffer)
    if (!config.allowedTypes.includes(bufferType)) {
      errors.push(`Invalid audio buffer type: ${bufferType}. Allowed: ${config.allowedTypes.join(', ')}`)
    }

    // Check buffer size
    const size = this.getBufferSize(audioBuffer)
    if (size === 0) {
      errors.push('Audio buffer cannot be empty')
    } else if (size > config.maxSize) {
      errors.push(`Audio buffer too large: ${size} bytes (max: ${config.maxSize})`)
    } else if (size < config.minSize) {
      errors.push(`Audio buffer too small: ${size} bytes (min: ${config.minSize})`)
    }

    return {
      valid: errors.length === 0,
      errors,
      metadata: {
        type: bufferType,
        size,
        sizeKB: Math.round(size / 1024 * 100) / 100
      }
    }
  }

  validateAudioFormat(format) {
    const supportedFormats = [
      'LINEAR16', 'FLAC', 'MULAW', 'AMR', 'AMR_WB', 'OGG_OPUS', 'SPEEX_WITH_HEADER_BYTE', 'WEBM_OPUS'
    ]

    return {
      valid: supportedFormats.includes(format),
      errors: supportedFormats.includes(format) ? [] : [`Unsupported audio format: ${format}`],
      supportedFormats
    }
  }

  // ===========================================
  // TEXT VALIDATION
  // ===========================================

  validateTextInput(text, options = {}) {
    const errors = []
    const config = {
      maxLength: options.maxLength || this.maxTextLength,
      minLength: options.minLength || 1,
      allowEmpty: options.allowEmpty || false,
      sanitize: options.sanitize !== false
    }

    // Type check
    if (typeof text !== 'string') {
      errors.push('Text input must be a string')
      return { valid: false, errors }
    }

    // Empty check
    const trimmedText = text.trim()
    if (!config.allowEmpty && trimmedText === '') {
      errors.push('Text input cannot be empty')
    }

    // Length validation
    if (trimmedText.length > config.maxLength) {
      errors.push(`Text too long: ${trimmedText.length} characters (max: ${config.maxLength})`)
    }

    if (trimmedText.length < config.minLength) {
      errors.push(`Text too short: ${trimmedText.length} characters (min: ${config.minLength})`)
    }

    // Content validation
    const contentValidation = this.validateTextContent(trimmedText)
    errors.push(...contentValidation.errors)

    return {
      valid: errors.length === 0,
      errors,
      sanitized: config.sanitize ? this.sanitizeText(trimmedText) : trimmedText,
      metadata: {
        originalLength: text.length,
        trimmedLength: trimmedText.length,
        wordCount: trimmedText.split(/\s+/).filter(word => word.length > 0).length
      }
    }
  }

  validateTextContent(text) {
    const errors = []
    const warnings = []

    // Check for potentially dangerous content
    const suspiciousPatterns = [
      { pattern: /<script/i, message: 'Potential script injection detected' },
      { pattern: /javascript:/i, message: 'JavaScript protocol detected' },
      { pattern: /on\w+\s*=/i, message: 'Event handler attributes detected' },
      { pattern: /data:text\/html/i, message: 'HTML data URI detected' }
    ]

    suspiciousPatterns.forEach(({ pattern, message }) => {
      if (pattern.test(text)) {
        errors.push(message)
      }
    })

    // Check for excessive repetition
    const repetitionCheck = this.checkTextRepetition(text)
    if (repetitionCheck.isExcessive) {
      warnings.push('Excessive text repetition detected')
    }

    return { errors, warnings, repetitionCheck }
  }

  sanitizeText(text) {
    return text
      // Remove potentially dangerous HTML/script content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim()
  }

  checkTextRepetition(text) {
    const words = text.toLowerCase().split(/\s+/)
    const wordCounts = {}

    words.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1
    })

    const maxCount = Math.max(...Object.values(wordCounts))
    const totalWords = words.length
    const repetitionRatio = maxCount / totalWords

    return {
      isExcessive: repetitionRatio > 0.3, // More than 30% repetition
      maxRepeatedWord: Object.keys(wordCounts).find(word => wordCounts[word] === maxCount),
      maxCount,
      repetitionRatio: Math.round(repetitionRatio * 100) / 100
    }
  }

  // ===========================================
  // CONVERSATION VALIDATION
  // ===========================================

  validateConversationHistory(history, options = {}) {
    const errors = []
    const config = {
      maxLength: options.maxLength || this.maxConversationHistory,
      requireContent: options.requireContent !== false,
      validateStructure: options.validateStructure !== false
    }

    // Type check
    if (!Array.isArray(history)) {
      errors.push('Conversation history must be an array')
      return { valid: false, errors }
    }

    // Length check
    if (history.length > config.maxLength) {
      errors.push(`Conversation history too long: ${history.length} entries (max: ${config.maxLength})`)
    }

    // Structure validation
    if (config.validateStructure) {
      history.forEach((turn, index) => {
        const turnValidation = this.validateConversationTurn(turn, index)
        errors.push(...turnValidation.errors)
      })
    }

    return {
      valid: errors.length === 0,
      errors,
      trimmed: history.length > config.maxLength ? history.slice(-config.maxLength) : history,
      metadata: {
        totalTurns: history.length,
        userTurns: history.filter(turn => turn.user).length,
        assistantTurns: history.filter(turn => !turn.user).length
      }
    }
  }

  validateConversationTurn(turn, index) {
    const errors = []

    // Structure validation
    if (!turn || typeof turn !== 'object') {
      errors.push(`Invalid conversation turn at index ${index}: must be an object`)
      return { errors }
    }

    // Required fields
    if (typeof turn.user !== 'boolean') {
      errors.push(`Invalid user flag at index ${index}: must be boolean`)
    }

    if (typeof turn.content !== 'string') {
      errors.push(`Invalid content at index ${index}: must be string`)
    } else if (turn.content.trim() === '') {
      errors.push(`Empty content at index ${index}`)
    }

    // Content validation
    if (turn.content) {
      const textValidation = this.validateTextInput(turn.content, { maxLength: 2000 })
      if (!textValidation.valid) {
        errors.push(`Content validation failed at index ${index}: ${textValidation.errors.join(', ')}`)
      }
    }

    return { errors }
  }

  // ===========================================
  // API VALIDATION
  // ===========================================

  validateApiKey(apiKey) {
    const errors = []

    if (!apiKey) {
      errors.push('API key is required')
      return { valid: false, errors }
    }

    if (typeof apiKey !== 'string') {
      errors.push('API key must be a string')
      return { valid: false, errors }
    }

    // Length validation
    if (apiKey.length < 8) {
      errors.push('API key too short (minimum 8 characters)')
    }

    if (apiKey.length > 256) {
      errors.push('API key too long (maximum 256 characters)')
    }

    // Pattern validation
    const validPattern = /^[a-zA-Z0-9_-]+$/
    if (!validPattern.test(apiKey)) {
      errors.push('API key contains invalid characters (only alphanumeric, underscore, hyphen allowed)')
    }

    return {
      valid: errors.length === 0,
      errors,
      metadata: {
        length: apiKey.length,
        masked: this.maskApiKey(apiKey)
      }
    }
  }

  validateRequestHeaders(headers) {
    const errors = []
    const warnings = []

    // // Check for required headers
    // const requiredHeaders = ['content-type', 'x-api-key']
    // requiredHeaders.forEach(header => {
    //   if (!headers[header] && !headers[header.toLowerCase()]) {
    //     errors.push(`Missing required header: ${header}`)
    //   }
    // })

    // // Validate content-type
    // const contentType = headers['content-type'] || headers['Content-Type']
    // if (contentType && !contentType.includes('application/json')) {
    //   warnings.push(`Unexpected content-type: ${contentType}`)
    // }

    // Check for suspicious headers
    const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip']
    suspiciousHeaders.forEach(header => {
      if (headers[header]) {
        warnings.push(`Proxy header detected: ${header}`)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  // ===========================================
  // CONFIGURATION VALIDATION
  // ===========================================

  validateProviderConfig(provider, config) {
    const errors = []

    if (!provider || typeof provider !== 'string') {
      errors.push('Provider name is required and must be a string')
      return { valid: false, errors }
    }

    if (!config || typeof config !== 'object') {
      errors.push('Provider configuration is required and must be an object')
      return { valid: false, errors }
    }

    // Provider-specific validation
    switch (provider.toLowerCase()) {
      case 'google':
        return this.validateGoogleConfig(config)
      case 'azure':
        return this.validateAzureConfig(config)
      default:
        errors.push(`Unsupported provider: ${provider}`)
        return { valid: false, errors }
    }
  }

  validateGoogleConfig(config) {
    const errors = []

    // Required fields
    if (!config.cloud?.project) {
      errors.push('Google Cloud project is required')
    }

    if (!config.tts?.baseURL) {
      errors.push('Google TTS base URL is required')
    }

    if (!config.tts?.apiKey) {
      errors.push('Google TTS API key is required')
    }

    // Optional validations
    if (config.speech?.sampleRate && (config.speech.sampleRate < 8000 || config.speech.sampleRate > 48000)) {
      errors.push('Invalid sample rate (must be between 8000 and 48000)')
    }

    if (config.ai?.temperature && (config.ai.temperature < 0 || config.ai.temperature > 1)) {
      errors.push('Invalid temperature (must be between 0 and 1)')
    }

    return { valid: errors.length === 0, errors }
  }

  validateAzureConfig(config) {
    const errors = []

    // Required fields
    if (!config.speech?.subscriptionKey) {
      errors.push('Azure Speech subscription key is required')
    }

    if (!config.speech?.region) {
      errors.push('Azure Speech region is required')
    }

    if (!config.openai?.endpoint) {
      errors.push('Azure OpenAI endpoint is required')
    }

    if (!config.openai?.apiKey) {
      errors.push('Azure OpenAI API key is required')
    }

    // URL validation
    if (config.openai?.endpoint && !this.isValidUrl(config.openai.endpoint)) {
      errors.push('Invalid Azure OpenAI endpoint URL')
    }

    return { valid: errors.length === 0, errors }
  }

  // ===========================================
  // UTILITY METHODS
  // ===========================================

  getBufferType(buffer) {
    if (Buffer.isBuffer(buffer)) return 'Buffer'
    if (buffer instanceof Uint8Array) return 'Uint8Array'
    if (buffer instanceof ArrayBuffer) return 'ArrayBuffer'
    return typeof buffer
  }

  getBufferSize(buffer) {
    if (Buffer.isBuffer(buffer)) return buffer.length
    if (buffer instanceof Uint8Array) return buffer.length
    if (buffer instanceof ArrayBuffer) return buffer.byteLength
    if (buffer && typeof buffer.length === 'number') return buffer.length
    if (buffer && typeof buffer.byteLength === 'number') return buffer.byteLength
    return 0
  }

  maskApiKey(apiKey) {
    if (!apiKey || apiKey.length < 8) return '***'
    return apiKey.substring(0, 4) + '***' + apiKey.substring(apiKey.length - 4)
  }

  isValidUrl(string) {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  isValidIPAddress(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  }

  // ===========================================
  // BATCH VALIDATION
  // ===========================================

  validateBatch(items, validator, options = {}) {
    const results = []
    const summary = {
      total: items.length,
      valid: 0,
      invalid: 0,
      errors: []
    }

    items.forEach((item, index) => {
      try {
        const result = validator(item, { ...options, index })
        results.push(result)

        if (result.valid) {
          summary.valid++
        } else {
          summary.invalid++
          summary.errors.push(`Item ${index}: ${result.errors.join(', ')}`)
        }
      } catch (error) {
        const errorResult = {
          valid: false,
          errors: [`Validation error: ${error.message}`],
          index
        }
        results.push(errorResult)
        summary.invalid++
        summary.errors.push(`Item ${index}: ${error.message}`)
      }
    })

    return {
      results,
      summary,
      allValid: summary.invalid === 0
    }
  }
}

// Create singleton instance
const validation = new ValidationSystem()

module.exports = { ValidationSystem, validation }
