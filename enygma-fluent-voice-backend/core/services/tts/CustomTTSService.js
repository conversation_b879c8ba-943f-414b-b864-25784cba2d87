// core/services/tts/CustomTTSService.js
// Auto-generado por update-tts-service.js
const axios = require('axios')

class CustomTTSService {
  constructor(config, logger) {
    this.config = config
    this.logger = logger

    this.axiosInstance = axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: this.config.timeout || 30000
    })
  }

  async testConnection() {
    try {
      const response = await this.axiosInstance.post('/available_voices', {}, {
        timeout: 5000
      })
      return { healthy: true, response: response.data }
    } catch (error) {
      this.logger.error('CustomTTS connection test failed:', error)
      return { healthy: false, error: error.message }
    }
  }

  async getAvailableVoices() {
    try {
      // Voces validadas y funcionando
      const validatedVoices = [
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>'
      ]

      return {
        voices: validatedVoices.map(voice => ({
          id: voice,
          name: voice,
          gender: this.getVoiceGender(voice),
          language: 'es-ES'
        })),
        total: validatedVoices.length
      }
    } catch (error) {
      this.logger.error('Error getting available voices:', error)
      throw error
    }
  }

  getVoiceGender(voiceId) {
    const femaleVoices = ['Dalia', 'Elvira', 'Irene', 'Triana', 'Ximena DragonHD']
    const maleVoices = ['Elias', 'Jorge', 'Saul']

    if (femaleVoices.includes(voiceId)) return 'female'
    if (maleVoices.includes(voiceId)) return 'male'
    return 'unknown'
  }

  async generateSpeech(text, options = {}) {
    try {
      const voiceId = options.voiceId || this.config.defaultVoice || 'Dalia'

      // Validar que la voz esté disponible
      const validatedVoices = ['Dalia', 'Elias', 'Elvira', 'Irene', 'Jorge', 'Saul', 'Triana', 'Ximena DragonHD']
      if (!validatedVoices.includes(voiceId)) {
        throw new Error(`Voice "${voiceId}" not available. Available voices: ${validatedVoices.join(', ')}`)
      }

      const payload = {
        input_text: text,
        voice_params: {
          voice_name: voiceId
        }
      }

      this.logger.debug('TTS Request:', { payload, url: this.config.apiUrl })

      const response = await this.axiosInstance.post('/t2s', payload, {
        responseType: 'arraybuffer',
        timeout: this.config.timeout || 15000
      })

      if (!response.data || response.data.byteLength === 0) {
        throw new Error('Empty audio response')
      }

      this.logger.debug('TTS Response:', {
        audioSize: response.data.byteLength,
        contentType: response.headers['content-type']
      })

      return {
        audio: response.data,
        format: 'mp3',
        voiceId,
        size: response.data.byteLength
      }

    } catch (error) {
      this.logger.error('TTS generation failed:', error)
      throw error
    }
  }
}

module.exports = CustomTTSService