/**
 * Centralized security configuration and middleware
 * Handles CORS, authentication, rate limiting, and security headers
 */
const cors = require('cors')
const hpp = require('hpp')
const cookieParser = require('cookie-parser')
const csurf = require('csurf')
const { environment } = require('./environment')
const { logger } = require('../utils/logger')

class SecurityManager {
  constructor() {
    this.config = environment.security
    this.performance = environment.performance
  }

  // ===========================================
  // EXPRESS MIDDLEWARE CONFIGURATION
  // ===========================================

  configureExpress(app) {
    logger.info('🔒 Configuring Express security middleware...')

    // Disable x-powered-by header
    app.disable('x-powered-by')

    // Security headers
    this.setSecurityHeaders(app)

    // Body parsing with size limits
    app.use(require('express').json({
      limit: this.config.maxRequestSize,
      strict: true
    }))

    // HTTP Parameter Pollution protection
    app.use(hpp())

    // Cookie parser
    app.use(cookieParser())

    // CORS configuration
    this.configureCors(app)

    // CSRF protection (if enabled)
    if (this.config.enableCSRF) {
      const csrfProtection = csurf({
        cookie: {
          httpOnly: true,
          secure: environment.isProduction,
          sameSite: 'strict'
        }
      })
      app.use('/api', csrfProtection)
    }

    logger.info('✅ Express security middleware configured')
  }

  // ===========================================
  // SECURITY HEADERS
  // ===========================================

  setSecurityHeaders(app) {
    app.use((req, res, next) => {
      // Prevent MIME type sniffing
      res.setHeader('X-Content-Type-Options', 'nosniff')

      // Prevent clickjacking
      res.setHeader('X-Frame-Options', 'DENY')

      // Force HTTPS in production
      if (environment.isProduction) {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
      }

      // Content Security Policy
      res.setHeader('Content-Security-Policy', this.getCSPHeader())

      // XSS Protection
      res.setHeader('X-XSS-Protection', '1; mode=block')

      // Referrer Policy
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')

      // Permissions Policy
      res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

      next()
    })
  }

  getCSPHeader() {
    const csp = [
      "default-src 'self'",
      "connect-src 'self' wss: ws:",
      "img-src 'self' data: blob:",
      "style-src 'self' 'unsafe-inline'", // Allow inline styles for dynamic content
      "font-src 'self'",
      "script-src 'self'",
      "object-src 'none'",
      "frame-ancestors 'none'",
      "base-uri 'self'"
    ]

    if (environment.isDevelopment) {
      // More permissive in development
      csp.push("script-src 'self' 'unsafe-eval'")
    }

    return csp.join('; ')
  }

  // ===========================================
  // CORS CONFIGURATION
  // ===========================================

  configureCors(app) {
    const corsOptions = {
      origin: (origin, callback) => {
        const allowedOrigins = this.config.corsOrigins

        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true)

        // Check if origin is allowed
        if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
          callback(null, true)
        } else {
          logger.warn(`🚫 CORS blocked request from: ${origin}`)
          callback(new Error(`CORS policy violation: ${origin} not allowed`))
        }
      },
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-API-Key',
        'X-Requested-With',
        'X-CSRF-Token'
      ],
      credentials: true,
      maxAge: 86400 // 24 hours
    }

    app.use(cors(corsOptions))

    // Handle preflight requests
    app.options('*', cors(corsOptions))
  }

  // ===========================================
  // SOCKET.IO SECURITY
  // ===========================================

  configureSocketSecurity(io) {
    logger.info('🔒 Configuring Socket.IO security...')

    // Authentication middleware
    io.use(this.socketAuth.bind(this))

    // Connection event handlers
    io.engine.on('connection_error', (err) => {
      logger.error('❌ Socket.IO connection error:', err.message)
    })

    io.on('connection', (socket) => {
      // Monitor socket activity
      this.monitorSocketActivity(socket)

      // Setup idle timeout
      this.setupIdleTimeout(socket)

      logger.debug(`🔌 Socket connected: ${socket.id} from ${socket.handshake.address}`)
    })

    logger.info('✅ Socket.IO security configured')
  }

  // ===========================================
  // AUTHENTICATION MIDDLEWARE
  // ===========================================

  validateApiKey(req, res, next) {
    const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '')

    if (!apiKey || apiKey !== this.config.apiKey) {
      logger.warn(`🚫 Unauthorized API access attempt from: ${req.ip}`, {
        url: req.url,
        userAgent: req.get('User-Agent'),
        apiKey: apiKey ? '***masked***' : 'missing'
      })

      return res.status(401).json({
        error: 'Unauthorized access',
        code: 'INVALID_API_KEY'
      })
    }

    // Add user context to request
    req.user = {
      authenticated: true,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestTime: new Date()
    }

    next()
  }

  socketAuth(socket, next) {
    const apiKey = socket.handshake.auth.apiKey || socket.handshake.headers['x-api-key']

    if (!apiKey || apiKey !== this.config.apiKey) {
      logger.warn(`🚫 Unauthorized socket connection attempt from: ${socket.handshake.address}`)
      return next(new Error('Authentication failed'))
    }

    // Add user context to socket
    socket.user = {
      authenticated: true,
      connectionTime: new Date(),
      ip: socket.handshake.address
    }

    next()
  }

  // ===========================================
  // RATE LIMITING
  // ===========================================

  createRateLimiter(options = {}) {
    if (!this.config.enableRateLimit) {
      return (req, res, next) => next() // No-op if disabled
    }

    const defaults = {
      windowMs: 60000, // 1 minute
      max: 100, // requests per window
      message: 'Too many requests from this IP',
      standardHeaders: true,
      legacyHeaders: false
    }

    const config = { ...defaults, ...options }

    // Simple in-memory rate limiter (for production, use Redis)
    const clients = new Map()

    return (req, res, next) => {
      const key = req.ip
      const now = Date.now()
      const windowStart = now - config.windowMs

      // Get or create client record
      let client = clients.get(key) || { requests: [], windowStart: now }

      // Remove old requests outside the window
      client.requests = client.requests.filter(time => time > windowStart)

      // Check if limit exceeded
      if (client.requests.length >= config.max) {
        logger.warn(`🚫 Rate limit exceeded for IP: ${key}`)
        return res.status(429).json({
          error: config.message,
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(config.windowMs / 1000)
        })
      }

      // Add current request
      client.requests.push(now)
      clients.set(key, client)

      // Clean up old clients periodically
      if (Math.random() < 0.01) { // 1% chance
        this.cleanupRateLimitClients(clients, windowStart)
      }

      next()
    }
  }

  cleanupRateLimitClients(clients, windowStart) {
    for (const [key, client] of clients.entries()) {
      if (client.requests.every(time => time <= windowStart)) {
        clients.delete(key)
      }
    }
  }

  // ===========================================
  // SOCKET MONITORING
  // ===========================================

  monitorSocketActivity(socket) {
    let messageCount = 0
    const maxMessages = 1000 // messages per minute
    const interval = 60000 // 1 minute

    const resetCounter = setInterval(() => {
      messageCount = 0
    }, interval)

    socket.on('audio', () => {
      messageCount++
      if (messageCount > maxMessages) {
        logger.warn(`🚫 Rate limit exceeded for socket: ${socket.id}`)
        socket.emit('error', {
          message: 'Rate limit exceeded',
          code: 'SOCKET_RATE_LIMIT'
        })
        socket.disconnect(true)
      }
    })

    socket.on('disconnect', () => {
      clearInterval(resetCounter)
    })
  }

  setupIdleTimeout(socket) {
    let idleTimeout = setTimeout(() => {
      logger.info(`⏰ Closing idle socket: ${socket.id}`)
      socket.emit('info', { message: 'Session timed out due to inactivity' })
      socket.disconnect(true)
    }, this.config.sessionTimeout)

    // Reset timeout on activity
    const resetTimeout = () => {
      clearTimeout(idleTimeout)
      idleTimeout = setTimeout(() => {
        logger.info(`⏰ Closing idle socket: ${socket.id}`)
        socket.disconnect(true)
      }, this.config.sessionTimeout)
    }

    socket.on('audio', resetTimeout)
    socket.on('transcription', resetTimeout)
    socket.on('ping', resetTimeout)

    socket.on('disconnect', () => {
      clearTimeout(idleTimeout)
    })
  }

  // ===========================================
  // VALIDATION HELPERS
  // ===========================================

  validateIPAddress(ip) {
    // Basic IP validation and blacklist check
    const blacklistedIPs = [
      '127.0.0.1', // Localhost (if needed)
      // Add other IPs as needed
    ]

    return !blacklistedIPs.includes(ip)
  }

  sanitizeUserAgent(userAgent) {
    // Remove potentially dangerous content from user agent
    return userAgent?.replace(/[<>\"'/\\]/g, '') || 'Unknown'
  }

  // ===========================================
  // SECURITY UTILITIES
  // ===========================================

  generateSecureToken(length = 32) {
    const crypto = require('crypto')
    return crypto.randomBytes(length).toString('hex')
  }

  hashApiKey(apiKey) {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(apiKey).digest('hex')
  }

  isSecureConnection(req) {
    return req.secure || req.headers['x-forwarded-proto'] === 'https'
  }

  // ===========================================
  // SECURITY AUDIT
  // ===========================================

  getSecurityStatus() {
    return {
      environment: environment.nodeEnv,
      security: {
        corsEnabled: true,
        csrfEnabled: this.config.enableCSRF,
        rateLimitEnabled: this.config.enableRateLimit,
        secureHeaders: true,
        apiKeyRequired: !!this.config.apiKey
      },
      allowedOrigins: this.config.corsOrigins.length,
      sessionTimeout: this.config.sessionTimeout,
      maxRequestSize: this.config.maxRequestSize
    }
  }
}

// Create singleton instance
const securityManager = new SecurityManager()

// Export both class and configured functions for convenience
module.exports = {
  SecurityManager,
  securityManager,

  // Convenience exports for backward compatibility
  configureExpress: (app) => securityManager.configureExpress(app),
  configureSocketSecurity: (io) => securityManager.configureSocketSecurity(io),
  validateApiKey: (req, res, next) => securityManager.validateApiKey(req, res, next),
  createRateLimiter: (options) => securityManager.createRateLimiter(options)
}
