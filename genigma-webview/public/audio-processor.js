class AudioProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    this.inputSampleRate = sampleRate;
    this.targetSampleRate = 16000;
    this.ratio = this.inputSampleRate / this.targetSampleRate;
    this.buffer = [];
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    if (input && input.length > 0) {
      const channelData = input[0];
      for (let i = 0; i < channelData.length; i++) {
        this.buffer.push(channelData[i]);
      }
      const outputSamples = [];
      while (this.buffer.length >= this.ratio) {
        const index = Math.floor(this.ratio / 2);
        outputSamples.push(this.buffer[index]);
        this.buffer.splice(0, Math.floor(this.ratio));
      }
      if (outputSamples.length > 0) {
        this.port.postMessage(outputSamples);
      }
    }
    return true;
  }
}

registerProcessor("audio-processor", AudioProcessor);
