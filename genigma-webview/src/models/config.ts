import type { GameMode } from "../contexts/EnygmaGameContext";

// ========== CONFIGURACIÓN DE MODOS DE JUEGO ==========
export interface GameModeConfig {
  id: string;
  enabled: boolean;
  image: string;
  mode: GameMode;
  buttonText: string;
  description: string;
  model: string;
  prompt: string;
}

export interface GameModesConfig {
  gameModes: GameModeConfig[];
}

// ========== CONFIGURACIÓN DE REGLAS ==========
export interface GameRuleConfig {
  id: number;
  title: string;
  image?: string;
  content?: string;
  description?: string;
}

export interface GameRulesConfig {
  title: string;
  pages: GameRuleConfig[];
}

// ========== CONFIGURACIÓN DE AUDIO ==========
export interface AudioConfig {
  backgroundMusic: {
    enabled: boolean;
    volume: number;
    tracks: string[];
    defaultTrack: string;
  };
  speechSynthesis: {
    enabled: boolean;
    volume: number;
    defaultVoice: string;
    rate: number;
    pitch: number;
  };
  soundEffects: {
    enabled: boolean;
    volume: number;
    sounds: Record<string, string>;
  };
}

// ========== CONFIGURACIÓN DE API ==========
export interface APIConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  endpoints: {
    ai: string;
    speech: string;
    transcription: string;
  };
  headers: Record<string, string>;
}

// ========== CONFIGURACIÓN DE SPEECH ==========
export interface SpeechConfig {
  azure: {
    enabled: boolean;
    subscriptionKey: string;
    region: string;
    voices: {
      male: string;
      female: string;
    };
    ssmlEnabled: boolean;
  };
  web: {
    enabled: boolean;
    fallbackEnabled: boolean;
    voices: string[];
  };
  coordination: {
    defaultPriority: string;
    defaultChannel: string;
    queueTimeout: number;
    maxRetries: number;
  };
}

// ========== CONFIGURACIÓN DE VALIDACIÓN ==========
export interface ValidationConfig {
  gameResponses: {
    enabled: boolean;
    strictMode: boolean;
    confidenceThreshold: number;
    patterns: Record<string, string[]>;
  };
  textNormalization: {
    removeAccents: boolean;
    toLowerCase: boolean;
    removeSpecialChars: boolean;
    maxLength: number;
  };
}

// ========== CONFIGURACIÓN DE UI ==========
export interface UIConfig {
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
  };
  animations: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
  responsive: {
    breakpoints: Record<string, number>;
  };
}

// ========== CONFIGURACIÓN DE DESARROLLO ==========
export interface DevelopmentConfig {
  debug: boolean;
  logging: {
    enabled: boolean;
    level: "error" | "warn" | "info" | "debug";
    console: boolean;
    file: boolean;
  };
  testing: {
    enabled: boolean;
    mockServices: boolean;
    simulationDelay: number;
  };
}

// ========== CONFIGURACIÓN PRINCIPAL ==========
export interface AppConfiguration {
  version: string;
  environment: "development" | "staging" | "production";

  // Configuraciones específicas
  gameModes: GameModesConfig;
  gameRules: GameRulesConfig;
  audio: AudioConfig;
  api: APIConfig;
  speech: SpeechConfig;
  validation: ValidationConfig;
  ui: UIConfig;
  development: DevelopmentConfig;

  // Configuraciones dinámicas
  features: Record<string, boolean>;
  experiments: Record<string, any>;

  // Metadatos
  lastUpdated: string;
  configVersion: string;
}

// ========== TIPOS DE CARGA DE CONFIGURACIÓN ==========
export interface ConfigLoadResult {
  success: boolean;
  config?: AppConfiguration;
  error?: string;
  warnings?: string[];
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ========== CONFIGURACIÓN DE ENTORNO ==========
export interface EnvironmentConfig {
  NODE_ENV: string;
  VITE_API_URL?: string;
  VITE_API_KEY?: string;
  VITE_SPEECH_API_URL?: string;
  VITE_SPEECH_API_KEY?: string;
  VITE_DEBUG?: string;
  VITE_MOCK_SERVICES?: string;
  VITE_PERPLEXITY_BASE_URL?: string;
  VITE_PERPLEXITY_API_KEY?: string;
  VITE_PERPLEXITY_PRESETID?: string;
}
