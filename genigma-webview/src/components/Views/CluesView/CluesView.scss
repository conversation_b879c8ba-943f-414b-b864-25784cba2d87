.clues-screen {
  flex: auto !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: auto;
  min-height: 100dvh;
  position: relative;

  h1 {
    margin-bottom: 16px;
  }

  .clues-list {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
    width: 100%;
    padding-bottom: 5rem;

    .clue-tag {
      background-color: #D6FDF1;
      user-select: none;
      pointer-events: none;
    }
  }

  .clues-container {
    padding: 12rem 200px 4rem 200px;
    width: 100%;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out forwards;

    @media (max-width: 1080px) {
      padding: 7rem 1rem 1rem 1rem;
    }
  }

  .empty-state {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }

  .clues-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    width: 100%;
    max-width: 600px;

    @media (max-width: 768px) {
      padding: 1rem;
    }
    .clues-title {
      font-family: Playfair Display;
      font-style: SemiBold;
      font-size: 1.5rem;
      text-align: center;
      width: 100%;
      text-align: left;
      color: #88FFD5;
      font-weight: 600;
      display: none;
      padding-bottom: 1rem;
      @media (max-width: 768px) {
        display: block;
      }
    }
  }

  .empty-state-frame {
    width: 100%;
    border: 2px solid #88FFD5;
    border-radius: 16px;
    box-shadow: 0px 0px 16px 0px #88FFD5;
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 600px;
    gap: 1rem;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }

  .clues-screen-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed !important;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    padding: 2rem;
    animation: fadeInUp 0.6s ease-out forwards;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
  
}
