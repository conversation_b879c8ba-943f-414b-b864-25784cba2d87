@use './../../../animations.scss';

// Welcome Screen
.welcome-screen-overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgb(0, 20, 40);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
  
    .welcome-screen {
      max-width: 600px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 2rem;
      padding: 2rem;
      animation: fadeInUp 0.6s ease-out forwards;
      
      .welcome-title {
        color: #88FFD5;
        text-shadow: 0 0 20px rgba(136, 255, 213, 0.5);
        text-align: center;
        font-family: Playfair Display;
        font-style: SemiBold;
        line-height: 100%;
      }
  
      .welcome-body {
        color: #e0e0e0;
        max-width: 500px;
        text-align: center;
      }
  
      .welcome-disclaimer {
        color: #94a3b8;
        opacity: 0;
        text-align: center;
        &.isInitializing {
            opacity: 1;
        }
      }
    }
}