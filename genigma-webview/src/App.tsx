import { useState, useEffect, useCallback, useRef } from "react";
// Components
import { CookieConsentBanner } from "./components/Auxiliar/CookieConsentBanner";
import { WelcomeScreen } from "./components/Views/WelcomeScreen/WelcomeScreen";
import { Header } from "./components/Header/Header";
import MainView from "./components/Views/MainView/MainView";
import PlayView from "./components/Views/PlayView/PlayView";
import RulesView from "./components/Views/RulesView/RulesView";
import CluesView from "./components/Views/CluesView/CluesView";
import GameResultView from "./components/Views/PlayView/GameResultView";
import { LoadBlock } from "microapps";
// Contexts
import { useAppContext, type AppState } from "./contexts/AppContext";
import type { GameMode } from "./contexts/EnygmaGameContext";
import { useEnygmaGame } from "./contexts/EnygmaGameContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
import { useSpeechCoordinator } from "./hooks/useSpeechCoordinator";
// Types
import type { ViewMode } from "./models/app";
// Services
import { audioManager } from "./services/AudioManager";
import { aiService } from "./services/AIService";
// Styles
import "./App.scss";

function App() {
  const { isInitialized, errors } = useAppContext();
  const { startGameFlow } = useGameOrchestrator();
  const { stopAll: stopAllSpeech } = useSpeechCoordinator();
  const { session } = useEnygmaGame();
  const [appState, setAppState] = useState<AppState>("loading");
  const [currentView, setCurrentView] = useState<ViewMode>("main");
  const [previousView, setPreviousView] = useState<ViewMode | null>(null);
  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);
  const [showExitPopup, setShowExitPopup] = useState<boolean>(false);
  const [isGeneratingCharacter, setIsGeneratingCharacter] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string | null>(null);

  // ========== EFECTOS ==========
  useEffect(() => {
    if (isInitialized) {
      const hasConsent = localStorage.getItem("enygma_analytics_consent");

      if (!hasConsent) {
        setAppState("consent");
      } else {
        setAppState("welcome");
      }
    }
  }, [isInitialized]);

  useEffect(() => {
    if (errors.length > 0) {
      console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // ========== HANDLERS ==========
  const navigateTo = useCallback(
    (newView: ViewMode) => {
      setPreviousView(currentView);
      setCurrentView(newView);
    },
    [currentView]
  );

  // Detectar cuando el juego termina y navegar a la vista de resultados
  const hasNavigatedToResult = useRef(false);

  useEffect(() => {
    if (session && session.phase === "finished" && currentView === "play" && !hasNavigatedToResult.current) {
      hasNavigatedToResult.current = true;
      setPreviousView(currentView);
      setCurrentView("result");
    }

    // Reset flag when not in finished state
    if (!session || session.phase !== "finished") {
      hasNavigatedToResult.current = false;
    }
  }, [session, currentView]);

  const navigateBack = useCallback(() => {
    if (previousView) {
      setCurrentView(previousView);
      setPreviousView(null);
    } else {
      setCurrentView("main");
    }
  }, [previousView]);

  const resetNavigationTo = useCallback((view: ViewMode) => {
    setPreviousView(null);
    setCurrentView(view);
  }, []);

  const handleAudioActivated = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleConsentGiven = useCallback(() => {
    setAppState("welcome");
  }, []);

  const handleWelcomeComplete = useCallback(() => {
    setAppState("ready");
    setHasPlayedWelcome(true);
  }, []);

  const handleStartGame = async (mode: GameMode) => {
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("clues");
    localStorage.removeItem("cuenta_regresiva");
    setIsStartingGame(true);

    try {
      if (!hasPlayedWelcome) {
        setHasPlayedWelcome(true);
      }

      await startGameFlow(mode);
      navigateTo("play");
    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleShowRules = () => navigateTo("rules");

  const handleShowClues = () => navigateTo("clues");

  // Añadir estos handlers en App.tsx:
  const handlePlayAgain = useCallback(() => {
    // Limpiar localStorage de personaje y resultado
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("enygma_character_timestamp");
    localStorage.removeItem("enygma_last_game_result");

    // Navegar a la vista principal para seleccionar modo
    resetNavigationTo("main");
  }, [resetNavigationTo]);

  const handleExistGame = () => {
    setShowExitPopup(true);
  };

  const handleConfirmExit = () => {
    // Limpiar variables de localStorage
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("enygma_character_timestamp");
    localStorage.removeItem("clues");
    localStorage.removeItem("cuenta_regresiva");

    // Detener cualquier audio de narración activo
    stopAllSpeech();
    audioManager.stopSpeech();

    // Navegar a la vista principal
    resetNavigationTo("main");

    // Cerrar el popup
    setShowExitPopup(false);
  };

  const handleCancelExit = () => {
    setShowExitPopup(false);
  };

  const handleBackToMain = () => navigateBack();
  const showBackButton = currentView !== "main" && currentView !== "play" && currentView !== "result";

  // ========== DESARROLLO - SELECT LIST Y GENERACIÓN DE PERSONAJE ==========
  const handleViewChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newView = event.target.value as ViewMode;
    navigateTo(newView);
  };

  const handleGenerateCharacter = async () => {
    setIsGeneratingCharacter(true);
    try {
      const character = await aiService.generateCharacter();
      setGeneratedCharacter(character);
      console.log("🎭 Personaje generado:", character);
    } catch (error) {
      console.error("❌ Error generando personaje:", error);
    } finally {
      setIsGeneratingCharacter(false);
    }
  };

  // ========== RENDERIZADO CONDICIONAL ==========
  if (appState === "loading") {
    return (
      <div className="loader-container">
        <div className="loader">
          <LoadBlock interval={6000} text="Cargando..." />
        </div>
      </div>
    );
  }

  const renderContent = () => {
    switch (appState) {
      case "consent":
        return (
          <CookieConsentBanner
            onAudioActivated={handleAudioActivated}
            onConsentGiven={handleConsentGiven}
          />
        );
      case "welcome":
        return <WelcomeScreen onGameReady={handleWelcomeComplete} />;
    }

    switch (currentView) {
      case "main":
        return (
          <MainView
            handleStartGame={handleStartGame}
            handleShowRules={handleShowRules}
            isStartingGame={isStartingGame}
            isReady={true}
          />
        );
      case "play":
        return (
          <PlayView
            handleShowClues={handleShowClues}
            handleExistGame={handleExistGame}
            showExitPopup={showExitPopup}
            handleConfirmExit={handleConfirmExit}
            handleCancelExit={handleCancelExit}
          />
        );
      case "rules":
        return (
          <RulesView
            isOpen={true}
            onClose={handleBackToMain}
            onStart={handleStartGame}
          />
        );
      case "clues":
        return <CluesView onBackToMain={handleBackToMain} />;
      case "result":
        return (
          <GameResultView
            onPlayAgain={handlePlayAgain}
            onBackToMain={handleBackToMain}
          />
        );
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <div className="App">
      {/* SELECT LIST PARA DESARROLLO - POSICIÓN ABSOLUTA */}
      {import.meta.env.DEV && (
        <div className="dev-view-selector">
          <select
            value={currentView}
            onChange={handleViewChange}
            className="view-select"
          >
            <option value="main">Main View</option>
            <option value="play">Play View</option>
            <option value="rules">Rules View</option>
            <option value="clues">Clues View</option>
            <option value="result">Result View</option>
            <option value="consent">Consent View</option>
            <option value="welcome">Welcome View</option>
          </select>
          <button
            onClick={handleGenerateCharacter}
            disabled={isGeneratingCharacter}
            className="generate-character-btn"
          >
            {isGeneratingCharacter ? "Generando..." : "Generar Personaje"}
          </button>
          {generatedCharacter && (
            <div className="generated-character">
              <strong>Personaje:</strong> {generatedCharacter}
            </div>
          )}
        </div>
      )}

      <div className="game-container">
        <img
          src="assets/game/background.png"
          alt="Background"
          className="background"
        />

        <div className="board">
          <Header
            currentView={currentView}
            onBackToMain={handleBackToMain}
            showBackButton={showBackButton}
          />
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default App;
