@font-face {
  font-family: "OnAir";
  src: url("./assets/fonts/OnAir-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "OnAir";
  src: url("./assets/fonts/OnAir-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "OnAir";
  src: url("./assets/fonts/OnAir-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: "OnAir";
  src: url("./assets/fonts/OnAir-LightItalic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: "OnAir";
  src: url("./assets/fonts/OnAir-Light.ttf") format("truetype");
  font-weight: 400;
}

@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap");

* {
  color: white;
}

html,
body {
  margin: 0;
  width: 100%;
  height: 100%;
  font-family: "OnAir";
  overflow: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  color: #88FFD5;
}
