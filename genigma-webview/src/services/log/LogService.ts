import { v4 as uuidv4 } from "uuid";
import type { LogEntry, LogLevel, LogFilter, Listener } from "./LogTypes";
import { LogFilterer } from "./LogFilterer";

export class LogService {
  private static instance: LogService;
  private logs: LogEntry[] = [];
  private listeners: Set<Listener> = new Set();

  private constructor() {}

  public static getInstance(): LogService {
    if (!LogService.instance) {
      LogService.instance = new LogService();
    }
    return LogService.instance;
  }

  public addLog<T>(
    level: LogLevel,
    service: string,
    message: string,
    data?: T,
    stack?: string
  ) {
    const entry: LogEntry<T> = {
      id: uuidv4(),
      timestamp: new Date(),
      service,
      level,
      message,
      data,
      stack,
    };
    this.logs.push(entry);
    this.notifyListeners();
  }

  public debug<T>(service: string, message: string, data?: T) {
    this.addLog("debug", service, message, data);
  }

  public info<T>(service: string, message: string, data?: T) {
    this.addLog("info", service, message, data);
  }

  public warn<T>(service: string, message: string, data?: T) {
    this.addLog("warn", service, message, data);
  }

  public error<T>(service: string, message: string, data?: T) {
    this.addLog("error", service, message, data);
  }

  public success<T>(service: string, message: string, data?: T) {
    this.addLog("success", service, message, data);
  }

  public getLogs<T>(filter?: Partial<LogFilter>): LogEntry<T>[] {
    if (!filter) return [...this.logs] as LogEntry<T>[];
    return LogFilterer.filter(this.logs as LogEntry<T>[], filter as LogFilter);
  }

  public clear() {
    this.logs = [];
    this.notifyListeners();
  }

  public subscribe(listener: Listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners() {
    const snapshot = [...this.logs];
    this.listeners.forEach((l) => l(snapshot));
  }
}
