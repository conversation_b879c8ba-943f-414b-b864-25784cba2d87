import type { LogEntry } from "./LogTypes";
import { rawConsole } from "./RawConsole";
import { LogService } from "./LogService";

export class ConsoleTransport {
  private logService: LogService;
  private unsubscribe?: () => void;

  constructor(logService: LogService) {
    this.logService = logService;
  }

  public start() {
    this.unsubscribe = this.logService.subscribe((entries) => {
      const entry = entries[entries.length - 1];
      if (entry) {
        this.logToConsole(entry);
      }
    });
  }

  public stop() {
    this.unsubscribe && this.unsubscribe();
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toLocaleTimeString();
    const prefix = `[${timestamp}] [${entry.service.toUpperCase()}]`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case "debug":
        rawConsole.log(`🔍 ${message}`, entry.data || "");
        break;
      case "info":
        rawConsole.info(`ℹ️ ${message}`, entry.data || "");
        break;
      case "warn":
        rawConsole.warn(`⚠️ ${message}`, entry.data || "");
        break;
      case "error":
        rawConsole.error(`❌ ${message}`, entry.data || "", entry.stack || "");
        break;
      case "success":
        rawConsole.log(`✅ ${message}`, entry.data || "");
        break;
    }
  }
}
