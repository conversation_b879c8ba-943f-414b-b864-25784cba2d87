/**
 * ========================================================================
 * REQUEST HELPER
 * ========================================================================
 *
 * Maneja las peticiones HTTP a la API de IA
 * Incluye retry logic, timeouts y manejo de errores
 * ========================================================================
 */

import type { APIPayload } from "../../../models/services";
import { API_CONFIG } from '../config/APIConfig';

export interface RequestOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface RequestResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  attempts: number;
}

export class RequestHelper {
  private serviceName = 'requestHelper';
  private defaultOptions: RequestOptions = {
    timeout: 30000, // 30 segundos
    retries: 3,
    retryDelay: 1000 // 1 segundo
  };

  // ========== MÉTODOS PRINCIPALES ==========

  /**
   * Realiza una petición HTTP con retry automático
   */
  public async makeRequest<T>(
    endpoint: string,
    payload: APIPayload,
    options: RequestOptions = {}
  ): Promise<RequestResult<T>> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const url = `${API_CONFIG.baseURL}${endpoint}`;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= mergedOptions.retries!; attempt++) {
      try {
        const result = await this.executeSingleRequest<T>(url, payload, mergedOptions.timeout!);

        if (result.success) {
          return {
            ...result,
            attempts: attempt
          };
        }

        lastError = new Error(result.error || 'Request failed');

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(`⚠️ [${this.serviceName}] Intento ${attempt} falló:`, lastError.message);
      }

      // Si no es el último intento, esperar antes del retry
      if (attempt < mergedOptions.retries!) {
        await this.delay(mergedOptions.retryDelay! * attempt);
      }
    }

    console.error(`❌ [${this.serviceName}] Todos los intentos fallaron`);
    return {
      success: false,
      error: lastError?.message || 'All retry attempts failed',
      attempts: mergedOptions.retries!
    };
  }

  /**
   * Realiza una petición POST simple sin retry
   */
  public async simpleRequest<T>(
    endpoint: string,
    payload: APIPayload,
    timeout: number = 30000
  ): Promise<RequestResult<T>> {
    const url = `${API_CONFIG.baseURL}${endpoint}`;

    try {
      return await this.executeSingleRequest<T>(url, payload, timeout);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error en petición simple:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        attempts: 1
      };
    }
  }

  // ========== MÉTODOS PRIVADOS ==========

  /**
   * Ejecuta una sola petición HTTP
   */
  private async executeSingleRequest<T>(
    url: string,
    payload: APIPayload,
    timeout: number
  ): Promise<RequestResult<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_CONFIG.apiKey,
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP error! status: ${response.status}`,
          statusCode: response.status,
          attempts: 1
        };
      }

      const data = await response.json();

      return {
        success: true,
        data: data as T,
        statusCode: response.status,
        attempts: 1
      };

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: `Request timeout after ${timeout}ms`,
          attempts: 1
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        attempts: 1
      };
    }
  }

  /**
   * Helper para delay entre retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // ========== MÉTODOS DE UTILIDAD ==========

  /**
   * Valida el payload antes de enviarlo
   */
  public validatePayload(payload: APIPayload): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!payload.preset) {
      errors.push('Preset is required');
    }

    if (!payload.query && !payload.model_params) {
      errors.push('Query or model_params is required');
    }

    if (!payload.id || !payload.id.clt) {
      errors.push('Client ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Construye headers personalizados
   */
  public buildHeaders(customHeaders: Record<string, string> = {}): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'X-API-Key': API_CONFIG.apiKey,
      ...customHeaders
    };
  }

  /**
   * Obtiene información de estado de la API
   */
  public async checkAPIHealth(): Promise<boolean> {
    try {
      const testPayload: APIPayload = {
        id: {
          clt: 'health-check',
          corr: `health-${Date.now()}`
        },
        preset: 'test',
        query: 'test',
        model_params: {
          max_tokens: 1
        }
      };

      const result = await this.simpleRequest('health', testPayload, 5000);
      return result.success;
    } catch {
      return false;
    }
  }

  /**
   * Calcula el tamaño estimado del payload
   */
  public getPayloadSize(payload: APIPayload): number {
    return new Blob([JSON.stringify(payload)]).size;
  }

  // /**
  //  * Logs detallados de la petición para debugging
  //  */
  // public logRequestDetails(endpoint: string, payload: APIPayload): void {
  //   if (import.meta.env.MODE === 'development') {
  //     console.group(`🔍 [${this.serviceName}] Request Details`);
  //     console.log('Endpoint:', endpoint);
  //     console.log('Payload size:', this.getPayloadSize(payload), 'bytes');
  //     console.log('Preset:', payload.preset);
  //     console.log('Query length:', payload.query?.length || 0);
  //     console.log('Session ID:', payload.id.ses || 'none');
  //     console.groupEnd();
  //   }
  // }
}

// ========== SINGLETON EXPORT ==========
export const requestHelper = new RequestHelper();
