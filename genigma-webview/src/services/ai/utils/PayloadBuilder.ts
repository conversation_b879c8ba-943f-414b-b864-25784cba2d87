/**
 * ========================================================================
 * PAYLOAD BUILDER
 * ========================================================================
 *
 * Construye payloads para diferentes tipos de peticiones a la API
 * Maneja templates, parámetros y configuraciones específicas
 * ========================================================================
 */

import type { APIPayload } from "../../../models/services";
import type { GenerateOptions } from '../models/AIModels';
import { API_CONFIG } from '../config/APIConfig';
import type { GameMode } from "../../../contexts/EnygmaGameContext";

export class PayloadBuilder {
  private serviceName = 'payloadBuilder';

  // ========== TEMPLATES PREDEFINIDOS ==========
  private templates = {
    iaVsPlayer: `[CONTEXTO: El personaje en el que debes pensar es "{character}"]

Pregunta del usuario: {MSG}

Responde en formato JSON según las instrucciones del sistema.`,

    characterGeneration: `Genera un personaje interesante para un juego de adivinanzas.
El personaje puede ser real o ficticio, pero debe ser conocido.`,

    resetSession: `Sesión reiniciada. Preparado para nuevo juego.`
  };

  // ========== MÉTODOS PRINCIPALES ==========

  /**
   * Construye payload para generación de respuestas
   */
  public buildGeneratePayload(options: GenerateOptions): APIPayload {
    const preset = this.getPresetForMode(options.mode);
    const template = this.getTemplateForMode(options.mode, options.character);

    const payload: APIPayload = {
      id: {
        ses: options.sessionId,
        clt: "aura-game-client",
        corr: `game-${Date.now()}`,
      },
      preset,
      query: options.query,
      prompt_params: template ? { template } : {},
      model_params: {
        max_tokens: options.maxTokens || 200,
      },
    };

    // this.logPayloadDetails(payload);
    return payload;
  }

  /**
   * Construye payload para generación de personajes
   */
  public buildCharacterPayload(sessionId?: string): APIPayload {
    const payload: APIPayload = {
      id: {
        ses: sessionId,
        clt: "aura-game-client",
        corr: `genchar-${Date.now()}`,
      },
      preset: API_CONFIG.presets.genCharBot,
      query: "Dime un personaje",
      prompt_params: {
        preamble: "",
      },
      model_params: {
        max_tokens: 100,
      },
    };

    // this.logPayloadDetails(payload);
    return payload;
  }

  /**
   * Construye payload para reset de sesión
   */
  public buildResetPayload(sessionId: string): APIPayload {
    const payload: APIPayload = {
      id: {
        ses: sessionId,
        clt: "aura-game-client",
        corr: `reset-${Date.now()}`,
      },
      preset: "",
      query: "",
      prompt_params: {},
      model_params: {
        max_tokens: 1,
      },
    };

    return payload;
  }

  /**
   * Construye payload personalizado con opciones avanzadas
   */
  public buildCustomPayload(
    query: string,
    preset: string,
    sessionId?: string,
    customOptions?: {
      template?: string;
      preamble?: string;
      maxTokens?: number;
      temperature?: number;
      topP?: number;
    }
  ): APIPayload {
    const payload: APIPayload = {
      id: {
        ses: sessionId,
        clt: "aura-game-client",
        corr: `custom-${Date.now()}`,
      },
      preset,
      query,
      prompt_params: {
        ...(customOptions?.template && { template: customOptions.template }),
        ...(customOptions?.preamble && { preamble: customOptions.preamble }),
      },
      model_params: {
        max_tokens: customOptions?.maxTokens || 200,
        ...(customOptions?.temperature && { temperature: customOptions.temperature }),
        ...(customOptions?.topP && { top_p: customOptions.topP }),
      },
    };

    // this.logPayloadDetails(payload);
    return payload;
  }

  // ========== MÉTODOS DE CONFIGURACIÓN ==========

  /**
   * Obtiene el preset apropiado para el modo de juego
   */
  public getPresetForMode(mode: GameMode): string {
    switch (mode) {
      case "ia_vs_player":
        return API_CONFIG.presets.aura;
      default:
        console.warn(`⚠️ [${this.serviceName}] Modo desconocido: ${mode}, usando preset por defecto`);
        return API_CONFIG.presets.user;
    }
  }

  /**
   * Obtiene el template apropiado para el modo de juego
   */
  public getTemplateForMode(mode: GameMode, character?: string): string | undefined {
    switch (mode) {
      case "ia_vs_player":
        if (character) {
          return this.templates.iaVsPlayer.replace("{character}", character);
        }
        return undefined;
      default:
        return undefined;
    }
  }

  // ========== MÉTODOS DE UTILIDAD ==========

  /**
   * Valida un payload antes de enviarlo
   */
  public validatePayload(payload: APIPayload): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validar ID
    if (!payload.id) {
      errors.push("ID is required");
    } else {
      if (!payload.id.clt) {
        errors.push("Client ID is required");
      }
    }

    // Validar preset
    if (!payload.preset && payload.query) {
      errors.push("Preset is required for queries");
    }

    // Validar model_params
    if (!payload.model_params) {
      errors.push("Model params are required");
    } else {
      if (payload.model_params.max_tokens && payload.model_params.max_tokens < 1) {
        errors.push("Max tokens must be at least 1");
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Clona un payload con modificaciones
   */
  public clonePayload(payload: APIPayload, modifications: Partial<APIPayload>): APIPayload {
    return {
      ...payload,
      ...modifications,
      id: {
        ...payload.id,
        ...(modifications.id || {}),
        corr: `cloned-${Date.now()}`, // Nuevo correlation ID
      },
      prompt_params: {
        ...payload.prompt_params,
        ...(modifications.prompt_params || {}),
      },
      model_params: {
        ...payload.model_params,
        ...(modifications.model_params || {}),
      },
    };
  }

  /**
   * Construye payload para healthcheck
   */
  public buildHealthCheckPayload(): APIPayload {
    return {
      id: {
        clt: "health-check",
        corr: `health-${Date.now()}`,
      },
      preset: "health",
      query: "ping",
      prompt_params: {},
      model_params: {
        max_tokens: 1,
      },
    };
  }

  /**
   * Obtiene el tamaño estimado de un payload
   */
  public getPayloadSize(payload: APIPayload): number {
    return new Blob([JSON.stringify(payload)]).size;
  }

  /**
   * Construye correlation ID único
   */
  public generateCorrelationId(prefix: string = 'req'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  // ========== MÉTODOS PRIVADOS ==========

  // private logPayloadDetails(payload: APIPayload): void {
  //   if (import.meta.env.MODE === 'development') {}
  // }
}

// ========== SINGLETON EXPORT ==========
export const payloadBuilder = new PayloadBuilder();
