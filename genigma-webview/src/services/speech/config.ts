/**
 * Configuración para el servicio de Azure Speech
 */
export const SPEECH_CONFIG = {
  LANGUAGE: "es",
  BASE_URL: import.meta.env.VITE_SPEECH_API_URL,
  API_KEY: import.meta.env.VITE_SPEECH_API_KEY,
  CACHE_TTL: 24 * 60 * 60 * 1000,
  REQUEST_TIMEOUT: 30000,
  RETRY_DELAY: 1000,
} as const;

export const VOICE_DEFAULTS = {
  RATE: 1.1,
  PITCH: "default",
  VOLUME: "default",
  STYLE: "general",
  OUTPUT_FORMAT: "mp3",
} as const;

/**
 * Valida que la configuración esté completa
 */
export function validateConfig(): void {
  if (!SPEECH_CONFIG.BASE_URL) {
    throw new Error("VITE_SPEECH_API_URL no está configurada");
  }

  if (!SPEECH_CONFIG.API_KEY) {
    throw new Error("VITE_SPEECH_API_KEY no está configurada");
  }
}
