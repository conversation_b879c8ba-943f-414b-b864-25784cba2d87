
import { SPEECH_CONFIG } from "./config";
import type { RetryRequest } from "../../models/speech";

/**
 * Sistema de cola de reintentos para manejar throttling de Azure
 */
export class RetryQueue {
  private queue: Array<RetryRequest<any>> = [];
  private isProcessing = false;
  private readonly retryDelay: number;

  constructor(retryDelay: number = SPEECH_CONFIG.RETRY_DELAY) {
    this.retryDelay = retryDelay;
  }

  /**
   * Maneja el throttling de Azure y encola peticiones si es necesario
   */
  public async handleThrottling<T>(request: RetryRequest<T>): Promise<T> {
    try {
      return await request();
    } catch (error: any) {
      if (error.response?.status === 429) {
        console.warn(
          "⚠️ [RetryQueue] ⚠️ Azure throttling detectado, encolando petición"
        );
        return this.queueRequest(request);
      }
      throw error;
    }
  }

  /**
   * Añade una petición a la cola de reintentos
   */
  private async queueRequest<T>(request: RetryRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * Procesa la cola de reintentos
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const request = this.queue.shift()!;

      try {
        await request();
        // Esperar entre requests para evitar throttling
        await new Promise((resolve) => setTimeout(resolve, this.retryDelay));
      } catch (error) {
        console.error("❌ [RetryQueue] ❌ Retry falló", error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * Obtiene el estado actual de la cola
   */
  public getStats() {
    return {
      length: this.queue.length,
      isProcessing: this.isProcessing,
    };
  }

  /**
   * Limpia la cola de reintentos
   */
  public clear(): void {
    this.queue = [];
    this.isProcessing = false;
  }
}
