import axios from "axios";
import { handleRequest } from "../_helpersService";
import type {
  IVoicesService,
  PerformanceStats,
} from "../../models/speech";
import {
  SPEECH_CONFIG,
  VOICE_DEFAULTS,
  validateConfig,
} from "./config";
import { AudioCache } from "./cache";
import { RetryQueue } from "./retryQueue";
import {
  optimizeTextForSpeech,
  validateTextForSpeech,
} from "./textUtils";

export type VoiceConfig = {
  availableVoices: { name: string; default: boolean }[];
  defaultVoice: string;
};

/**
 * Servicio de Azure Voices para síntesis de voz - CORREGIDO
 */
export class AzureVoicesService implements IVoicesService {
  private static instance: AzureVoicesService;

  // ===== CORE =====
  private voiceId: string = "";
  private genre: string = "";
  private config?: VoiceConfig;
  private availableVoicesList: string[] = [];

  // ===== SISTEMAS =====
  private audioCache: AudioCache;
  private retryQueue: RetryQueue;

  private constructor() {
    this.audioCache = new AudioCache();
    this.retryQueue = new RetryQueue();
  }

  public static getInstance(): AzureVoicesService {
    if (!AzureVoicesService.instance) {
      AzureVoicesService.instance = new AzureVoicesService();
    }
    return AzureVoicesService.instance;
  }

  // ========== GETTERS Y SETTERS ==========
  public getCurrentVoiceId(): string {
    return this.voiceId;
  }

  public getAvailableVoicesList(): string[] {
    return this.availableVoicesList;
  }

  private setVoiceId(voice: string): void {
    this.voiceId = voice;
  }

  private setGenre(genre: string): void {
    this.genre = genre;
  }

  // ========== CONFIGURACIÓN Y ESTADO ==========
  private async loadConfig(): Promise<VoiceConfig> {
    if (!this.config) {
      const res = await fetch('/azure-voice-conf.json');
      if (!res.ok) {
        throw new Error(`Error cargando configuración de voz: HTTP ${res.status}`);
      }
      this.config = await res.json();
    }
    return this.config!;
  }

  public async getDefaultVoice(): Promise<string> {
    const { availableVoices, defaultVoice } = await this.loadConfig();
    this.availableVoicesList = availableVoices.map(v => v.name);
    return defaultVoice;
  }

  public async setVoiceByName(name: string): Promise<boolean> {
    const voices = await this.getAvailableVoices();
    this.availableVoicesList = voices;
    if (voices.includes(name)) {
      this.setVoiceId(name);
      return true;
    }
    console.warn(`⚠️ La voz "${name}" no está disponible.`);
    return false;
  }

  public reset(): void {
    // Core
    this.voiceId = "";
    this.genre = "";
    this.availableVoicesList = [];

    // Sistemas
    this.audioCache.clear();
    this.retryQueue.clear();
  }

  // ========== HEADERS CORREGIDOS ==========
  private getHeaders(): Record<string, string> {
    if (!SPEECH_CONFIG.API_KEY) {
      throw new Error("VITE_SPEECH_API_KEY no está configurada");
    }

    // ✅ FORMATO CORRECTO: Solo Authorization, sin Content-Type ni Accept
    return {
      'Authorization': `Bearer ${SPEECH_CONFIG.API_KEY}`,
    };
  }

  // ========== API DE AZURE ==========
  public async getAvailableVoices(): Promise<string[]> {
    validateConfig();

    const data = {
      language: SPEECH_CONFIG.LANGUAGE,
      gender: this.genre
    };

    try {
      const response = await handleRequest<string[]>(
        axios.post(`${SPEECH_CONFIG.BASE_URL}available_voices`, data, {
          headers: this.getHeaders()
        })
      );

      return response;
    } catch (error) {
      console.error('❌ [AzureVoicesService] Error obteniendo voces:', error);
      throw error;
    }
  }

  public async configVoice(genre: string): Promise<boolean> {
    this.setGenre(genre);

    try {
      // const voices = await this.getAvailableVoices();
      const voice = await this.getDefaultVoice();
      return this.setVoiceByName(voice);
    } catch (error) {
      console.error("❌ [AzureVoicesService] Error configurando voz", error);
      this.availableVoicesList = [];
      return false;
    }
  }

  // ========== MAIN - CORREGIDO CON FORMATO FUNCIONAL ==========
  public async getAudio(text: string): Promise<Blob> {
    validateConfig();

    if (!this.voiceId) {
      throw new Error("Azure Speech no configurado correctamente");
    }

    if (!validateTextForSpeech(text)) {
      throw new Error("Texto no válido para síntesis");
    }

    const cachedAudio = this.audioCache.get(text, this.voiceId);
    if (cachedAudio) {
      return cachedAudio;
    }

    const optimizedText = optimizeTextForSpeech(text);

    return this.retryQueue.handleThrottling(async () => {
      // ✅ FORMATO CORRECTO QUE FUNCIONA
      const correctPayload = {
        input_text: optimizedText,
        voice_params: {
          voice_id: this.voiceId,
          rate: VOICE_DEFAULTS.RATE
        },
        output_format: VOICE_DEFAULTS.OUTPUT_FORMAT
      };

      try {
        const response = await handleRequest<Blob>(
          axios.post(`${SPEECH_CONFIG.BASE_URL}t2s`, correctPayload, {
            responseType: "blob",
            headers: this.getHeaders(), // Sin Content-Type ni Accept
          })
        );

        this.audioCache.set(text, this.voiceId, response);
        return response;

      } catch (error) {
        console.error('❌ [AzureVoicesService] Error generando audio:', error);
        throw error;
      }
    });
  }

  // ========== UTILIDADES ==========
  public cleanupCache(): void {
    this.audioCache.cleanup();
  }

  public getPerformanceStats(): PerformanceStats {
    return {
      cache: this.audioCache.getStats(),
      queue: this.retryQueue.getStats(),
      voice: {
        configured: Boolean(this.voiceId),
        voiceId: this.voiceId,
        availableCount: this.availableVoicesList.length,
      },
    };
  }
}
