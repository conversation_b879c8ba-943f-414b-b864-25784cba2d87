import axios from 'axios';
import type { GenerateInput, GenerateResponse, IPerplexityService, PresetResponse, ResetResponse, SelectedPresetResponse } from '../models';

const BASE_URL = import.meta.env.VITE_PERPLEXITY_BASE_URL;
const API_KEY = import.meta.env.VITE_PERPLEXITY_API_KEY;
// const PRESET_ID = import.meta.env.VITE_PERPLEXITY_PRESETID!;

export class PerplexityService implements IPerplexityService {
    private sesid = '';
    private presetId = '';

    constructor() { }

    setSesid(id: string): void {
        this.sesid = id;
    }

    setPresetId(id: string): void {
        console.log('id: ', id);
        this.presetId = id;
    }

    private getHeaders(): Record<string, string> {
        return {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            'X-Api-Key': API_KEY || '',
            'X-MG-Ses': this.sesid,
            'X-Frame-Options': 'SAMEORIGIN'
        };
    }

    private async handleRequest<T>(request: Promise<any>): Promise<T> {
        try {
            const res = await request;
            if (import.meta.env.MODE === 'development') {
                console.log(`🟢 ${res.status} ${res.config.url}`, res.data);
            }
            return res.data as T;
        } catch (error) {
            if (import.meta.env.MODE === 'development') {
                if (error instanceof Error) {
                    console.error(`🔴 RESPONSE ERROR: ${error.message}`);
                } else {
                    console.error('🔴 RESPONSE ERROR: Unknown error', error);
                }
            }
            throw new Error('Error al realizar la solicitud');
        }
    }

    async generate(text: string, id?: string): Promise<GenerateResponse> {
        let data: GenerateInput = {
            sesid: this.sesid,
            preset: this.presetId,
            query: text,
            query_args: {}
        };
        if (id) {
            data.query_args.query_id = id;
        }

        return this.handleRequest<GenerateResponse>(
            axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
        );
    }

    async reset(): Promise<ResetResponse> {
        if (this.sesid !== "") {
            return this.handleRequest<ResetResponse>(
                axios.get(`${BASE_URL}reset/${this.sesid}?preset=${this.presetId}`, { headers: this.getHeaders() })
            );
        } else {
            return Promise.reject('reset');
        }
    }

    async selectPreset(): Promise<SelectedPresetResponse> {
        return this.handleRequest<SelectedPresetResponse>(
            axios.get(`${BASE_URL}preset/${this.presetId}`, { headers: this.getHeaders() })
        );
    }

    async getPresets(): Promise<PresetResponse> {
        console.log('getPresets:  ');
        console.log('getPresets2:  ', `${BASE_URL}presets`);
        return this.handleRequest<PresetResponse>(
            axios.get(`${BASE_URL}presets`, { headers: this.getHeaders() })
        );
    }
}

// Export singleton instance
export const perplexityService = new PerplexityService();
