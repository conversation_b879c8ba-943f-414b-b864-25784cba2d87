/**
 * ========================================================================
 * CHARACTER ENHANCEMENT SERVICE
 * ========================================================================
 *
 * Servicio que mejora los personajes generados por IA con información
 * de contenido de Movistar Plus para enriquecer la experiencia de juego
 * ========================================================================
 */

import { movistarAPIService } from './MovistarAPIService';
import type { MovistarContent } from '../models/services';

// ========== TIPOS ==========
export interface EnhancedCharacter {
  name: string;
  source: 'ai' | 'manual';
  movistarContent?: MovistarContent;
  hints: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  category: 'real' | 'fictional' | 'historical' | 'unknown';
  enhancementStatus: 'pending' | 'enhanced' | 'failed' | 'not_found';
}

export interface CharacterEnhancementOptions {
  enableMovistarSearch: boolean;
  maxRetries: number;
  fallbackToGeneric: boolean;
  generateHints: boolean;
}

// ========== CLASE PRINCIPAL ==========
export class CharacterEnhancementService {
  private serviceName = 'CharacterEnhancementService';
  private defaultOptions: CharacterEnhancementOptions = {
    enableMovistarSearch: true,
    maxRetries: 2,
    fallbackToGeneric: true,
    generateHints: true,
  };

  // ========== MÉTODO PRINCIPAL ==========
  /**
   * Mejora un personaje con información de Movistar Plus
   * @param characterName Nombre del personaje a mejorar
   * @param options Opciones de mejora
   * @returns Personaje mejorado con información adicional
   */
  public async enhanceCharacter(
    characterName: string,
    options: Partial<CharacterEnhancementOptions> = {}
  ): Promise<EnhancedCharacter> {
    const mergedOptions = { ...this.defaultOptions, ...options };

    console.log(`🔍 [${this.serviceName}] Mejorando personaje: "${characterName}"`);

    // Crear estructura base del personaje
    const baseCharacter: EnhancedCharacter = {
      name: characterName.trim(),
      source: 'ai',
      hints: [],
      difficulty: 'medium',
      category: 'unknown',
      enhancementStatus: 'pending'
    };

    // Si la búsqueda de Movistar está deshabilitada, devolver personaje básico
    if (!mergedOptions.enableMovistarSearch) {
      console.log(`ℹ️ [${this.serviceName}] Búsqueda de Movistar deshabilitada`);
      return this.createFallbackCharacter(baseCharacter, mergedOptions);
    }

    try {
      // Buscar contenido en Movistar Plus
      const movistarContent = await this.searchMovistarContent(characterName, mergedOptions.maxRetries);

      if (movistarContent) {
        // Mejorar personaje con información de Movistar
        return this.enhanceWithMovistarContent(baseCharacter, movistarContent, mergedOptions);
      } else {
        // No se encontró contenido, usar fallback
        console.log(`ℹ️ [${this.serviceName}] No se encontró contenido en Movistar para: "${characterName}"`);
        return this.createFallbackCharacter(baseCharacter, mergedOptions);
      }
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error mejorando personaje:`, error);
      return this.createFallbackCharacter(baseCharacter, mergedOptions);
    }
  }

  // ========== MÉTODOS PRIVADOS ==========
  /**
   * Busca contenido en Movistar Plus con reintentos
   */
  private async searchMovistarContent(
    characterName: string,
    maxRetries: number
  ): Promise<MovistarContent | null> {
    let attempts = 0;

    while (attempts < maxRetries) {
      try {
        console.log(`🔍 [${this.serviceName}] Intento ${attempts + 1}/${maxRetries} para: "${characterName}"`);

        const content = await movistarAPIService.searchByCharacter(characterName);

        if (content) {
          console.log(`✅ [${this.serviceName}] Contenido encontrado: "${content.title}"`);
          return content;
        }

        attempts++;

        // Pausa entre intentos
        if (attempts < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.warn(`⚠️ [${this.serviceName}] Error en intento ${attempts + 1}:`, error);
        attempts++;
      }
    }

    return null;
  }

  /**
   * Mejora el personaje con información de Movistar
   */
  private enhanceWithMovistarContent(
    baseCharacter: EnhancedCharacter,
    movistarContent: MovistarContent,
    options: CharacterEnhancementOptions
  ): EnhancedCharacter {
    const enhanced: EnhancedCharacter = {
      ...baseCharacter,
      movistarContent,
      enhancementStatus: 'enhanced',
      category: this.determineCategory(movistarContent),
      difficulty: this.calculateDifficulty(movistarContent),
    };

    // Generar pistas si está habilitado
    if (options.generateHints) {
      enhanced.hints = this.generateHintsFromContent(movistarContent);
    }

    console.log(`✅ [${this.serviceName}] Personaje mejorado exitosamente`);
    return enhanced;
  }

  /**
   * Crea un personaje de fallback cuando no se encuentra contenido
   */
  private createFallbackCharacter(
    baseCharacter: EnhancedCharacter,
    options: CharacterEnhancementOptions
  ): EnhancedCharacter {
    const fallback: EnhancedCharacter = {
      ...baseCharacter,
      enhancementStatus: 'not_found',
      category: this.guessCategory(baseCharacter.name),
      difficulty: 'medium',
    };

    // Generar pistas genéricas si está habilitado
    if (options.generateHints) {
      fallback.hints = this.generateGenericHints(baseCharacter.name);
    }

    return fallback;
  }

  /**
   * Determina la categoría del personaje basado en el contenido de Movistar
   */
  private determineCategory(content: MovistarContent): 'real' | 'fictional' | 'historical' | 'unknown' {
    const type = content.type?.toLowerCase() || '';
    const description = content.description?.toLowerCase() || '';

    // Buscar indicadores de contenido ficticio
    if (type.includes('movie') || type.includes('series') || type.includes('animation') || type.includes('content')) {
      return 'fictional';
    }

    // Buscar indicadores de contenido histórico/documental
    if (type.includes('documentary') || description.includes('historia') || description.includes('documental')) {
      return 'historical';
    }

    // Si es una persona real
    if (type.includes('person')) {
      return 'real';
    }

    // Por defecto, asumir ficticio para contenido de entretenimiento
    return 'fictional';
  }

  /**
   * Calcula la dificultad basada en el contenido
   */
  private calculateDifficulty(content: MovistarContent): 'easy' | 'medium' | 'hard' {
    let score = 0;

    // Factores que aumentan la dificultad
    if (content.year && content.year < 2000) score += 2;
    if (content.type?.includes('documentary')) score += 1;
    if (content.genre?.includes('indie') || content.genre?.includes('art')) score += 2;

    // Factores que disminuyen la dificultad
    if (content.year && content.year > 2010) score -= 1;
    if (content.type?.includes('movie') || content.type?.includes('series')) score -= 1;

    if (score >= 2) return 'hard';
    if (score >= 1) return 'medium';
    return 'easy';
  }

  /**
   * Genera pistas basadas en el contenido de Movistar
   */
  private generateHintsFromContent(content: MovistarContent): string[] {
    const hints: string[] = [];

    if (content.type) {
      hints.push(`Aparece en contenido de tipo: ${content.type}`);
    }

    if (content.year) {
      hints.push(`Relacionado con contenido del año: ${content.year}`);
    }

    if (content.genre) {
      hints.push(`Género: ${content.genre}`);
    }

    if (content.title) {
      hints.push(`Título relacionado: "${content.title}"`);
    }

    return hints.slice(0, 3); // Máximo 3 pistas
  }

  /**
   * Adivina la categoría basada solo en el nombre
   */
  private guessCategory(name: string): 'real' | 'fictional' | 'historical' | 'unknown' {
    const lowerName = name.toLowerCase();

    // Indicadores de personajes históricos
    const historicalIndicators = ['rey', 'reina', 'emperador', 'presidente', 'general', 'santo', 'san'];
    if (historicalIndicators.some(indicator => lowerName.includes(indicator))) {
      return 'historical';
    }

    // Por defecto, desconocido
    return 'unknown';
  }

  /**
   * Genera pistas genéricas cuando no hay contenido de Movistar
   */
  private generateGenericHints(name: string): string[] {
    const hints: string[] = [];

    hints.push(`Es un personaje conocido`);
    hints.push(`Su nombre tiene ${name.length} caracteres`);

    // Añadir pista sobre la primera letra
    if (name.length > 0) {
      hints.push(`Su nombre empieza por la letra "${name[0].toUpperCase()}"`);
    }

    return hints;
  }

  // ========== MÉTODOS PÚBLICOS DE UTILIDAD ==========
  /**
   * Verifica si el servicio está configurado correctamente
   */
  public isConfigured(): boolean {
    return movistarAPIService.isConfigured();
  }

  /**
   * Obtiene estadísticas del servicio
   */
  public getServiceInfo() {
    return {
      serviceName: this.serviceName,
      movistarConfigured: movistarAPIService.isConfigured(),
      defaultOptions: this.defaultOptions,
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const characterEnhancementService = new CharacterEnhancementService();
