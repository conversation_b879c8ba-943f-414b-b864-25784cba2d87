import axios from 'axios';
import { textNormalizer } from './TextNormalizationService';

// ========== HELPER FUNCTIONS ==========

/**
 * Maneja las peticiones HTTP de manera consistente
 * @param request Promesa de la petición axios
 * @returns Datos de la respuesta o lanza error
 */
export async function handleRequest<T>(request: Promise<any>): Promise<T> {
  try {
    const res = await request;

    if (import.meta.env.MODE === "development") {
      console.log(
        `🟢 API Request: ${res.status} ${res.config?.url || "Unknown URL"}`,
      );
    }

    return res.data as T;
  } catch (error) {
    if (import.meta.env.MODE === "development") {
      if (axios.isAxiosError(error)) {
        console.error(`🔴 API ERROR: ${error.message}`);
        console.error(`🔴 Status: ${error.response?.status}`);
        console.error(`🔴 Status Text: ${error.response?.statusText}`);
        console.error(`🔴 URL: ${error.config?.url}`);
        console.error(`🔴 Headers enviados:`, error.config?.headers);

        // 🔍 CRÍTICO: Leer el cuerpo del error como JSON
        if (error.response?.data) {
          if (error.response.data instanceof Blob) {
            try {
              const errorText = await error.response.data.text();
              const errorJson = JSON.parse(errorText);
              console.error(`🔴 Error JSON Response:`, errorJson);

              // Esto nos dirá exactamente qué formato espera la API
              if (errorJson.message) {
                console.error(`🔴 Error Message:`, errorJson.message);
              }
              if (errorJson.details || errorJson.errors) {
                console.error(`🔴 Error Details:`, errorJson.details || errorJson.errors);
              }
            } catch (parseError) {
              console.error(`🔴 Error parsing error response:`, parseError);
              console.error(`🔴 Raw error data:`, error.response.data);
            }
          } else {
            console.error(`🔴 Error Response Body:`, error.response.data);
          }
        }
      } else {
        console.error("🔴 API ERROR: Unknown error", error);
      }
    }

    throw error;
  }
}

/**
 * Valida que las variables de entorno necesarias estén configuradas
 * @param requiredVars Array de nombres de variables requeridas
 * @throws Error si alguna variable no está configurada
 */
export function validateEnvironmentVariables(requiredVars: string[]): void {
  const missing = requiredVars.filter((varName) => !import.meta.env[varName]);

  if (missing.length > 0) {
    throw new Error(`Variables de entorno faltantes: ${missing.join(", ")}`);
  }
}

/**
 * Genera un ID único para correlación de requests
 * @param prefix Prefijo opcional para el ID
 * @returns String único para identificar la petición
 */
export function generateCorrelationId(prefix: string = "req"): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * Sanitiza texto para uso seguro en APIs
 * @param text Texto a sanitizar
 * @param maxLength Longitud máxima permitida
 * @returns Texto sanitizado
 * @deprecated Usar textNormalizer.sanitizeForAPI() directamente
 */
export function sanitizeText(text: string, maxLength: number = 1000): string {
  return textNormalizer.sanitize(text, {
    maxLength,
    removeHtmlChars: true,
    preserveBasicPunctuation: true
  });
}

/**
 * Delay asíncrono para testing y retry logic
 * @param ms Milisegundos a esperar
 * @returns Promesa que se resuelve después del delay
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Retry automático para operaciones que pueden fallar
 * @param operation Función que retorna una promesa
 * @param maxRetries Número máximo de reintentos
 * @param delayMs Delay entre reintentos en milisegundos
 * @returns Resultado de la operación o error final
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000,
): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (import.meta.env.MODE === "development") {
        // console.warn(
        //   `🔄 Retry attempt ${attempt}/${maxRetries} failed:`,
        //   lastError.message,
        // );
      }

      if (attempt < maxRetries) {
        await delay(delayMs * attempt); // Exponential backoff
      }
    }
  }

  throw lastError || new Error("Operation failed after retries");
}

/**
 * Formatea bytes en formato legible
 * @param bytes Número de bytes
 * @returns String formateado (ej: "1.5 MB")
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Debounce para evitar llamadas excesivas
 * @param func Función a ejecutar
 * @param wait Tiempo de espera en milisegundos
 * @returns Función debounced
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
