import { LogService } from './log/LogService';
import { ConsoleInterceptor } from './log/ConsoleInterceptor';
import { ConsoleTransport } from './log/ConsoleTransport';

export const log = LogService.getInstance();

export function startConsoleLogging() {
  const interceptor = new ConsoleInterceptor(log);
  interceptor.start();
  return interceptor;
}

export function startConsoleTransport() {
  const transport = new ConsoleTransport(log);
  transport.start();
  return transport;
}
