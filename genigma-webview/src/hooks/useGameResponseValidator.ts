// hooks/useGameResponseValidator.ts
/**
 * Hook que utiliza el servicio centralizado de validación de respuestas
 * Elimina duplicación y proporciona una API consistente
 */
import { useCallback, useMemo } from 'react';
import {
  gameResponseValidator,
  type GameResponseType,
  type ValidationResult
} from '../services/GameResponseValidationService';

export type { GameResponseType, ValidationResult };

// ========== CUSTOM HOOK ==========
export const useGameResponseValidator = () => {
  // ========== CORE VALIDATION ==========
  const validateResponse = useCallback((text: string): ValidationResult => {
    return gameResponseValidator.validate(text);
  }, []);

  // ========== QUICK VALIDATION ==========
  const isValidResponse = useCallback((text: string): boolean => {
    return gameResponseValidator.isValidResponse(text);
  }, []);

  const getResponseType = useCallback((text: string): GameResponseType => {
    return gameResponseValidator.getResponseType(text);
  }, []);

  // ========== CONFIDENCE & ALTERNATIVES ==========
  const getConfidenceScore = useCallback((text: string): number => {
    return gameResponseValidator.getConfidenceScore(text);
  }, []);

  const getAlternatives = useCallback((text: string): GameResponseType[] => {
    return gameResponseValidator.getAlternativeResponses(text);
  }, []);

  const isAmbiguous = useCallback((text: string): boolean => {
    return gameResponseValidator.isAmbiguous(text);
  }, []);

  // ========== SUGGESTIONS & HELP ==========
  const getSuggestions = useCallback((text: string): string[] => {
    return gameResponseValidator.getSuggestions(text);
  }, []);

  const getSupportedResponses = useCallback((): string[] => {
    return gameResponseValidator.getSupportedResponses();
  }, []);

  const getResponseHelp = useCallback((): string => {
    return gameResponseValidator.getResponseHelp();
  }, []);

  const getDetailedHelp = useCallback((): string => {
    return `${gameResponseValidator.getResponseHelp()}\n\nEjemplos válidos:\n• Sí: "sí", "claro", "por supuesto"\n• No: "no", "para nada", "jamás"\n• Tal vez: "quizás", "puede ser", "depende"\n• No lo sé: "no sé", "ni idea", "desconozco"`;
  }, []);

  // ========== BATCH VALIDATION ==========
  const validateMultiple = useCallback((texts: string[]): ValidationResult[] => {
    return texts.map(text => gameResponseValidator.validate(text));
  }, []);

  const getBestMatch = useCallback((texts: string[]): ValidationResult | null => {
    const results = validateMultiple(texts);
    const valid = results.filter(r => r.type !== "invalid");

    if (valid.length === 0) return null;

    return valid.reduce((best, current) =>
      current.confidence > best.confidence ? current : best
    );
  }, [validateMultiple]);

  // ========== ANALYTICS ==========
  const getValidationStats = useCallback((texts: string[]): {
    total: number;
    valid: number;
    invalid: number;
    ambiguous: number;
    averageConfidence: number;
    typeDistribution: Record<GameResponseType, number>;
  } => {
    const results = texts.map(text => gameResponseValidator.validate(text));

    const stats = {
      total: results.length,
      valid: results.filter(r => r.type !== "invalid").length,
      invalid: results.filter(r => r.type === "invalid").length,
      ambiguous: results.filter(r => r.isAmbiguous).length,
      averageConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
      typeDistribution: {
        yes: 0, no: 0, maybe: 0, unknown: 0, invalid: 0
      } as Record<GameResponseType, number>
    };

    results.forEach(result => {
      stats.typeDistribution[result.type]++;
    });

    return stats;
  }, []);

  // ========== MEMOIZED VALUES ==========
  const responseTypes = useMemo(() => ["yes", "no", "maybe", "unknown", "invalid"] as const, []);

  const confidenceThresholds = useMemo(() => ({
    high: 0.8,
    medium: 0.6,
    low: 0.4
  } as const), []);

  // ========== RETURN HOOK API ==========
  return {
    // Core validation
    validateResponse,
    isValidResponse,
    getResponseType,

    // Confidence & alternatives
    getConfidenceScore,
    getAlternatives,
    isAmbiguous,

    // Suggestions & help
    getSuggestions,
    getSupportedResponses,
    getResponseHelp,
    getDetailedHelp,

    // Batch validation
    validateMultiple,
    getBestMatch,

    // Analytics
    getValidationStats,

    // Constants
    responseTypes,
    confidenceThresholds
  };
};
