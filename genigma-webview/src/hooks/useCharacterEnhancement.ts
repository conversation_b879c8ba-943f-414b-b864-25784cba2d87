/**
 * ========================================================================
 * USE CHARACTER ENHANCEMENT HOOK
 * ========================================================================
 *
 * Hook personalizado para manejar la mejora de personajes con información
 * de Movistar Plus y su integración con el flujo del juego
 * ========================================================================
 */

import { useState, useCallback, useRef } from 'react';
import { characterEnhancementService } from '../services/CharacterEnhancementService';
import type { EnhancedCharacter, CharacterEnhancementOptions } from '../services/CharacterEnhancementService';

// ========== TIPOS ==========
export interface UseCharacterEnhancementState {
  isEnhancing: boolean;
  enhancedCharacter: EnhancedCharacter | null;
  error: string | null;
  lastEnhancementTime: Date | null;
}

export interface UseCharacterEnhancementActions {
  enhanceCharacter: (characterName: string, options?: Partial<CharacterEnhancementOptions>) => Promise<EnhancedCharacter | null>;
  clearEnhancement: () => void;
  retryEnhancement: () => Promise<EnhancedCharacter | null>;
  isServiceConfigured: () => boolean;
}

export interface UseCharacterEnhancementReturn extends UseCharacterEnhancementState, UseCharacterEnhancementActions {}

// ========== HOOK PRINCIPAL ==========
export function useCharacterEnhancement(): UseCharacterEnhancementReturn {
  // ========== ESTADO ==========
  const [state, setState] = useState<UseCharacterEnhancementState>({
    isEnhancing: false,
    enhancedCharacter: null,
    error: null,
    lastEnhancementTime: null,
  });

  // Referencias para retry
  const lastCharacterRef = useRef<string | null>(null);
  const lastOptionsRef = useRef<Partial<CharacterEnhancementOptions> | undefined>(undefined);

  // ========== ACCIONES ==========
  /**
   * Mejora un personaje con información de Movistar Plus
   */
  const enhanceCharacter = useCallback(
    async (
      characterName: string,
      options?: Partial<CharacterEnhancementOptions>
    ): Promise<EnhancedCharacter | null> => {
      if (!characterName || characterName.trim().length === 0) {
        console.warn('⚠️ [useCharacterEnhancement] Nombre de personaje vacío');
        return null;
      }

      // Guardar parámetros para retry
      lastCharacterRef.current = characterName;
      lastOptionsRef.current = options;

      setState(prev => ({
        ...prev,
        isEnhancing: true,
        error: null,
      }));

      try {
        console.log(`🔍 [useCharacterEnhancement] Iniciando mejora para: "${characterName}"`);
        
        const enhanced = await characterEnhancementService.enhanceCharacter(characterName, options);
        
        setState(prev => ({
          ...prev,
          isEnhancing: false,
          enhancedCharacter: enhanced,
          lastEnhancementTime: new Date(),
        }));

        console.log(`✅ [useCharacterEnhancement] Mejora completada:`, enhanced.enhancementStatus);
        return enhanced;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
        
        console.error(`❌ [useCharacterEnhancement] Error mejorando personaje:`, error);
        
        setState(prev => ({
          ...prev,
          isEnhancing: false,
          error: errorMessage,
        }));

        return null;
      }
    },
    []
  );

  /**
   * Limpia el estado de mejora
   */
  const clearEnhancement = useCallback(() => {
    setState({
      isEnhancing: false,
      enhancedCharacter: null,
      error: null,
      lastEnhancementTime: null,
    });
    
    lastCharacterRef.current = null;
    lastOptionsRef.current = undefined;
    
    console.log('🧹 [useCharacterEnhancement] Estado limpiado');
  }, []);

  /**
   * Reintenta la última mejora
   */
  const retryEnhancement = useCallback(async (): Promise<EnhancedCharacter | null> => {
    if (!lastCharacterRef.current) {
      console.warn('⚠️ [useCharacterEnhancement] No hay personaje previo para reintentar');
      return null;
    }

    console.log(`🔄 [useCharacterEnhancement] Reintentando mejora para: "${lastCharacterRef.current}"`);
    return enhanceCharacter(lastCharacterRef.current, lastOptionsRef.current);
  }, [enhanceCharacter]);

  /**
   * Verifica si el servicio está configurado
   */
  const isServiceConfigured = useCallback((): boolean => {
    return characterEnhancementService.isConfigured();
  }, []);

  // ========== RETORNO ==========
  return {
    // Estado
    isEnhancing: state.isEnhancing,
    enhancedCharacter: state.enhancedCharacter,
    error: state.error,
    lastEnhancementTime: state.lastEnhancementTime,
    
    // Acciones
    enhanceCharacter,
    clearEnhancement,
    retryEnhancement,
    isServiceConfigured,
  };
}

// ========== HOOK AUXILIAR PARA INTEGRACIÓN CON EL JUEGO ==========
/**
 * Hook especializado para usar la mejora de personajes en el contexto del juego
 */
export function useGameCharacterEnhancement() {
  const enhancement = useCharacterEnhancement();

  /**
   * Mejora un personaje generado por IA para el juego
   */
  const enhanceGameCharacter = useCallback(
    async (characterName: string): Promise<EnhancedCharacter | null> => {
      // Opciones específicas para el juego
      const gameOptions: Partial<CharacterEnhancementOptions> = {
        enableMovistarSearch: true,
        maxRetries: 2,
        fallbackToGeneric: true,
        generateHints: true,
      };

      return enhancement.enhanceCharacter(characterName, gameOptions);
    },
    [enhancement]
  );

  /**
   * Obtiene pistas del personaje mejorado para mostrar al jugador
   */
  const getGameHints = useCallback((): string[] => {
    if (!enhancement.enhancedCharacter) {
      return [];
    }

    return enhancement.enhancedCharacter.hints;
  }, [enhancement.enhancedCharacter]);

  /**
   * Obtiene información del contenido de Movistar para mostrar al final del juego
   */
  const getMovistarInfo = useCallback(() => {
    if (!enhancement.enhancedCharacter?.movistarContent) {
      return null;
    }

    const content = enhancement.enhancedCharacter.movistarContent;
    return {
      title: content.title,
      description: content.description,
      year: content.year,
      type: content.type,
      image: content.image,
    };
  }, [enhancement.enhancedCharacter]);

  /**
   * Verifica si el personaje tiene información de Movistar
   */
  const hasMovistarContent = useCallback((): boolean => {
    return Boolean(enhancement.enhancedCharacter?.movistarContent);
  }, [enhancement.enhancedCharacter]);

  return {
    ...enhancement,
    enhanceGameCharacter,
    getGameHints,
    getMovistarInfo,
    hasMovistarContent,
  };
}
