/**
 * ========================================================================
 * HOOK: useSpeechCoordinator
 * ========================================================================
 *
 * Hook para usar el coordinador de speech de forma reactiva
 * Proporciona métodos convenientes y estado en tiempo real
 * ========================================================================
 */

import { useState, useEffect, useCallback } from "react";
import { speechCoordinator } from "../services/SpeechCoordinator";
import type { SpeechState, SpeechType, UseSpeechCoordinatorReturn } from "../models";

/**
 * Hook para usar el coordinador de speech
 */
export const useSpeechCoordinator = (): UseSpeechCoordinatorReturn => {
  const [state, setState] = useState<SpeechState>(speechCoordinator.getState());

  // Suscribirse a cambios de estado
  useEffect(() => {
    const unsubscribe = speechCoordinator.subscribe(setState);
    return unsubscribe;
  }, []);

  // ========== MÉTODOS PRINCIPALES ==========
  const speak = useCallback(async (text: string, type: SpeechType = "info"): Promise<void> => {
    return speechCoordinator.speak(text, type);
  }, []);

  const speakError = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakError(text);
  }, []);

  const speakValidation = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakValidation(text);
  }, []);

  const speakGameResponse = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakGameResponse(text);
  }, []);

  const speakGameQuestion = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakGameQuestion(text);
  }, []);

  const speakWelcome = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakWelcome(text);
  }, []);

  const speakHint = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakHint(text);
  }, []);

  const speakInfo = useCallback(async (text: string): Promise<void> => {
    return speechCoordinator.speakInfo(text);
  }, []);

  // ========== CONTROL ==========
  const interrupt = useCallback((): void => {
    speechCoordinator.interrupt("critical");
  }, []);

  const clearQueue = useCallback((): void => {
    speechCoordinator.clearQueue();
  }, []);

  const stopAll = useCallback((): void => {
    speechCoordinator.stopAll();
  }, []);

  const speakWeb = useCallback(async (text: string, type: SpeechType = "info"): Promise<void> => {
    return speechCoordinator.speakWeb(text, type);
  }, []);

  // ========== UTILIDADES ==========
  const speakWithCallback = useCallback(async (
    text: string,
    type: SpeechType,
    onComplete: () => void
  ): Promise<void> => {
    return speechCoordinator.speakWithCallback(text, type, onComplete);
  }, []);

  // ========== PROPIEDADES COMPUTADAS ==========
  const isSpeaking = state.isPlaying;
  const queueLength = state.queue.length;
  const currentSpeech = state.currentRequest?.text || null;

  return {
    // Estado
    state,
    isSpeaking,
    queueLength,
    currentSpeech,

    // Métodos principales
    speak,
    speakError,
    speakValidation,
    speakGameResponse,
    speakGameQuestion,
    speakWelcome,
    speakHint,
    speakInfo,

    // Control
    interrupt,
    clearQueue,
    stopAll,

    // Canales específicos
    speakWeb,

    // Utilidades
    speakWithCallback,
  };
};

/**
 * Hook simplificado para casos básicos
 */
export const useSimpleSpeech = () => {
  const { speak, speakError, speakInfo, isSpeaking, stopAll } = useSpeechCoordinator();

  return {
    speak,
    speakError,
    speakInfo,
    isSpeaking,
    stopAll,
  };
};

/**
 * Hook específico para el juego
 */
export const useGameSpeech = () => {
  const {
    speakGameResponse,
    speakGameQuestion,
    speakValidation,
    speakHint,
    speakWelcome,
    interrupt,
    isSpeaking,
    queueLength
  } = useSpeechCoordinator();

  return {
    speakResponse: speakGameResponse,
    speakQuestion: speakGameQuestion,
    speakValidation,
    speakHint,
    speakWelcome,
    interrupt,
    isSpeaking,
    queueLength,
  };
};
